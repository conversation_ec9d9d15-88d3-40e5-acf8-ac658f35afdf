import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useServices, Service } from "../contexts/ServicesContext"

const { width } = Dimensions.get("window")

export default function CategoryServicesScreen({ route, navigation }: any) {
  const { categoryId } = route.params
  const { colors } = useTheme()
  const { categories, getServicesByCategory } = useServices()
  
  const [searchQuery, setSearchQuery] = useState("")
  
  const category = categories.find(cat => cat.id === categoryId)
  const services = getServicesByCategory(categoryId)
  
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    service.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const navigateToService = (serviceId: string) => {
    navigation.navigate("ServiceDetail", { serviceId })
  }

  const formatPrice = (service: Service) => {
    return `$${service.priceRange.min.toLocaleString()} - $${service.priceRange.max.toLocaleString()}`
  }

  const formatDuration = (service: Service) => {
    if (service.duration.min === service.duration.max) {
      return `${service.duration.min} ${service.duration.unit}`
    }
    return `${service.duration.min}-${service.duration.max} ${service.duration.unit}`
  }

  if (!category) {
    return (
      <View style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
        <Text style={[styles.errorText, { color: colors.error }]}>Categoría no encontrada</Text>
      </View>
    )
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={[styles.backButton, { color: colors.primary }]}>← Volver</Text>
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.categoryIcon}>{category.icon}</Text>
          <View>
            <Text style={[styles.categoryTitle, { color: colors.text }]}>{category.name}</Text>
            <Text style={[styles.categoryDescription, { color: colors.textSecondary }]}>
              {category.description}
            </Text>
          </View>
        </View>
      </View>

      {/* Search */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
        <Text style={[styles.searchIcon, { color: colors.textSecondary }]}>🔍</Text>
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder={`Buscar en ${category.name}...`}
          placeholderTextColor={colors.textLight}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Services List */}
        <View style={styles.servicesContainer}>
          {filteredServices.map((service) => (
            <TouchableOpacity
              key={service.id}
              style={[styles.serviceCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={() => navigateToService(service.id)}
            >
              <View style={styles.serviceHeader}>
                <Text style={styles.serviceIcon}>{service.icon}</Text>
                <View style={styles.serviceInfo}>
                  <View style={styles.serviceTitleRow}>
                    <Text style={[styles.serviceName, { color: colors.text }]}>{service.name}</Text>
                    {service.popular && (
                      <View style={[styles.popularBadge, { backgroundColor: colors.warning }]}>
                        <Text style={styles.popularText}>Popular</Text>
                      </View>
                    )}
                  </View>
                  <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
                    {service.description}
                  </Text>
                  
                  {/* Tags */}
                  <View style={styles.tagsContainer}>
                    {service.tags.slice(0, 3).map((tag, index) => (
                      <View key={index} style={[styles.tag, { backgroundColor: colors.primaryLight }]}>
                        <Text style={[styles.tagText, { color: colors.primary }]}>{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              </View>

              <View style={styles.serviceFooter}>
                <View style={styles.priceContainer}>
                  <Text style={[styles.servicePrice, { color: '#10B981' }]}>
                    {formatPrice(service)}
                  </Text>
                  <Text style={[styles.servicePriceUnit, { color: colors.textLight }]}>
                    por {service.priceRange.unit}
                  </Text>
                </View>
                
                <View style={styles.durationContainer}>
                  <Text style={[styles.serviceDuration, { color: colors.textSecondary }]}>
                    ⏱️ {formatDuration(service)}
                  </Text>
                </View>
              </View>

              {/* Requirements */}
              {service.requirements && service.requirements.length > 0 && (
                <View style={styles.requirementsContainer}>
                  <Text style={[styles.requirementsTitle, { color: colors.textSecondary }]}>
                    ℹ️ Incluye:
                  </Text>
                  {service.requirements.slice(0, 2).map((req, index) => (
                    <Text key={index} style={[styles.requirement, { color: colors.textLight }]}>
                      • {req}
                    </Text>
                  ))}
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {filteredServices.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>🔍</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No se encontraron servicios
            </Text>
            <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
              {searchQuery 
                ? `No hay servicios que coincidan con "${searchQuery}"`
                : "Esta categoría no tiene servicios disponibles"
              }
            </Text>
          </View>
        )}

        {/* Quick Actions */}
        <View style={[styles.quickActions, { backgroundColor: colors.surface }]}>
          <Text style={[styles.quickActionsTitle, { color: colors.text }]}>
            ¿No encuentras lo que buscas?
          </Text>
          <TouchableOpacity
            style={[styles.customRequestButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.navigate("CreateTask", { category: category.name })}
          >
            <Text style={styles.customRequestText}>✏️ Crear Solicitud Personalizada</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  backButton: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 16,
  },
  headerInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  categoryTitle: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 24,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
  },
  searchIcon: {
    fontSize: 18,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  servicesContainer: {
    paddingHorizontal: 24,
  },
  serviceCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  serviceHeader: {
    flexDirection: "row",
    marginBottom: 16,
  },
  serviceIcon: {
    fontSize: 28,
    marginRight: 16,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: "600",
    flex: 1,
  },
  popularBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "bold",
  },
  serviceDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: "500",
  },
  serviceFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  priceContainer: {
    flex: 1,
  },
  servicePrice: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 2,
  },
  servicePriceUnit: {
    fontSize: 12,
  },
  durationContainer: {
    alignItems: "flex-end",
  },
  serviceDuration: {
    fontSize: 14,
    fontWeight: "500",
  },
  requirementsContainer: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#F1F3F4",
  },
  requirementsTitle: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  requirement: {
    fontSize: 12,
    lineHeight: 16,
  },
  emptyContainer: {
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  quickActions: {
    margin: 24,
    padding: 20,
    borderRadius: 16,
    alignItems: "center",
  },
  quickActionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 16,
    textAlign: "center",
  },
  customRequestButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  customRequestText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    fontSize: 16,
    textAlign: "center",
    marginTop: 100,
  },
})
