import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { useUser } from '../contexts/UserContext'
import { useTheme } from '../contexts/ThemeContext'

export default function RoleSwitcher() {
  const { user, switchRole, canSwitchToRole } = useUser()
  const { colors } = useTheme()

  if (!user || !user.roles || user.roles.length <= 1) {
    return null // No mostrar si solo tiene un rol
  }

  const handleRoleSwitch = (role: "client" | "worker") => {
    if (user && canSwitchToRole(role) && user.currentRole !== role) {
      switchRole(role)
    }
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.surface }]}>
      <Text style={[styles.label, { color: colors.textSecondary }]}>
        Cambiar modo:
      </Text>
      
      <View style={styles.switchContainer}>
        <TouchableOpacity
          style={[
            styles.roleButton,
            {
              backgroundColor: user?.currentRole === "client" ? colors.primary : colors.surfaceGray,
              borderColor: colors.borderLight
            }
          ]}
          onPress={() => handleRoleSwitch("client")}
          disabled={user?.currentRole === "client"}
        >
          <Text style={[
            styles.roleText,
            {
              color: user?.currentRole === "client" ? colors.white : colors.textSecondary,
              fontWeight: user?.currentRole === "client" ? '600' : '400'
            }
          ]}>
            👤 Cliente
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.roleButton,
            {
              backgroundColor: user?.currentRole === "worker" ? colors.primary : colors.surfaceGray,
              borderColor: colors.borderLight
            }
          ]}
          onPress={() => handleRoleSwitch("worker")}
          disabled={user?.currentRole === "worker"}
        >
          <Text style={[
            styles.roleText,
            {
              color: user?.currentRole === "worker" ? colors.white : colors.textSecondary,
              fontWeight: user?.currentRole === "worker" ? '600' : '400'
            }
          ]}>
            🧑‍🔧 Trabajador
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  label: {
    fontSize: 14,
    marginRight: 12,
    fontWeight: '500',
  },
  switchContainer: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: 'transparent',
    borderRadius: 8,
    overflow: 'hidden',
  },
  roleButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    marginHorizontal: 2,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 13,
    textAlign: 'center',
  },
})
