import React, { createContext, useContext, useState } from "react"

export interface Service {
  id: string
  name: string
  category: string
  description: string
  icon: string
  priceRange: {
    min: number
    max: number
    unit: string // "hora", "servicio", "m2", etc.
  }
  duration: {
    min: number
    max: number
    unit: string // "horas", "días"
  }
  popular: boolean
  requirements?: string[]
  tags: string[]
}

export interface ServiceCategory {
  id: string
  name: string
  icon: string
  color: string
  description: string
  services: Service[]
}

interface ServicesContextType {
  categories: ServiceCategory[]
  getServicesByCategory: (categoryId: string) => Service[]
  getServiceById: (serviceId: string) => Service | undefined
  searchServices: (query: string) => Service[]
  getPopularServices: () => Service[]
}

const ServicesContext = createContext<ServicesContextType | undefined>(undefined)

// Datos de servicios basados en precios colombianos 2024-2025
const serviceCategories: ServiceCategory[] = [
  {
    id: "populares",
    name: "Proyectos Populares",
    icon: "⭐",
    color: "#FF6B35",
    description: "Los servicios más solicitados",
    services: [
      {
        id: "ensamble-muebles",
        name: "Ensamble de Muebles",
        category: "populares",
        description: "Armado de muebles IKEA, camas, escritorios y más",
        icon: "🪑",
        priceRange: { min: 30000, max: 80000, unit: "servicio" },
        duration: { min: 1, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Herramientas incluidas"],
        tags: ["muebles", "ikea", "armado", "ensamble"]
      },
      {
        id: "limpieza-hogar",
        name: "Limpieza del Hogar",
        category: "populares",
        description: "Limpieza completa de casa o apartamento",
        icon: "🧹",
        priceRange: { min: 25000, max: 60000, unit: "servicio" },
        duration: { min: 2, max: 6, unit: "horas" },
        popular: true,
        requirements: ["Productos incluidos"],
        tags: ["limpieza", "hogar", "casa"]
      },
      {
        id: "reparacion-electrodomesticos",
        name: "Reparación de Electrodomésticos",
        category: "populares",
        description: "Reparación de lavadoras, neveras, estufas y más",
        icon: "🔧",
        priceRange: { min: 40000, max: 120000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Diagnóstico incluido"],
        tags: ["electrodomésticos", "reparación", "lavadora", "nevera"]
      },
      {
        id: "instalacion-tv",
        name: "Instalación de TV",
        category: "populares",
        description: "Montaje de TV en pared y configuración de cables",
        icon: "📺",
        priceRange: { min: 35000, max: 70000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Soporte incluido"],
        tags: ["tv", "montaje", "instalación"]
      },
      {
        id: "plomeria-basica",
        name: "Plomería Básica",
        category: "populares",
        description: "Reparación de grifos, destapes y fugas menores",
        icon: "🚰",
        priceRange: { min: 25000, max: 60000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Herramientas incluidas"],
        tags: ["plomería", "grifo", "destape", "fuga"]
      },
      {
        id: "pintura-habitacion",
        name: "Pintura de Habitación",
        category: "populares",
        description: "Pintura completa de habitación o sala",
        icon: "🎨",
        priceRange: { min: 80000, max: 200000, unit: "habitación" },
        duration: { min: 4, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Pintura y materiales incluidos"],
        tags: ["pintura", "habitación", "sala"]
      }
    ]
  },
  {
    id: "limpieza",
    name: "Limpieza",
    icon: "🧹",
    color: "#4F46E5",
    description: "Servicios de limpieza para hogar y oficina",
    services: [
      {
        id: "limpieza-casa",
        name: "Limpieza General de Casa",
        category: "limpieza",
        description: "Limpieza completa de casa o apartamento incluyendo baños, cocina y habitaciones",
        icon: "🏠",
        priceRange: { min: 25000, max: 60000, unit: "servicio" },
        duration: { min: 2, max: 6, unit: "horas" },
        popular: true,
        requirements: ["Productos de limpieza incluidos"],
        tags: ["casa", "apartamento", "limpieza general"]
      },
      {
        id: "limpieza-profunda",
        name: "Limpieza Profunda",
        category: "limpieza",
        description: "Limpieza detallada incluyendo ventanas, electrodomésticos y áreas difíciles",
        icon: "✨",
        priceRange: { min: 45000, max: 90000, unit: "servicio" },
        duration: { min: 4, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Productos especializados incluidos", "Equipo profesional"],
        tags: ["limpieza profunda", "detallada", "ventanas"]
      },
      {
        id: "limpieza-oficina",
        name: "Limpieza de Oficina",
        category: "limpieza",
        description: "Limpieza de espacios comerciales y oficinas",
        icon: "🏢",
        priceRange: { min: 30000, max: 80000, unit: "servicio" },
        duration: { min: 2, max: 5, unit: "horas" },
        popular: false,
        requirements: ["Horarios flexibles", "Productos comerciales"],
        tags: ["oficina", "comercial", "empresarial"]
      },
      {
        id: "limpieza-mudanza",
        name: "Limpieza Post-Mudanza",
        category: "limpieza",
        description: "Limpieza completa después de mudanza o antes de entrega",
        icon: "📦",
        priceRange: { min: 40000, max: 85000, unit: "servicio" },
        duration: { min: 3, max: 7, unit: "horas" },
        popular: true,
        requirements: ["Limpieza intensiva", "Productos incluidos"],
        tags: ["mudanza", "entrega", "intensiva"]
      },
      {
        id: "limpieza-detallada",
        name: "Limpieza Detallada",
        category: "limpieza",
        description: "Limpieza detallada incluyendo ventanas, electrodomésticos",
        icon: "✨",
        priceRange: { min: 80000, max: 150000, unit: "servicio" },
        duration: { min: 4, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Productos especializados", "Equipo profesional"],
        tags: ["profunda", "detallada", "ventanas"]
      },
      {
        id: "limpieza-oficina",
        name: "Limpieza de Oficina",
        category: "limpieza",
        description: "Limpieza de espacios comerciales y oficinas",
        icon: "🏢",
        priceRange: { min: 2000, max: 4000, unit: "m2" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: false,
        requirements: ["Horarios flexibles", "Productos comerciales"],
        tags: ["oficina", "comercial", "empresarial"]
      },
      {
        id: "limpieza-ventanas",
        name: "Limpieza de Ventanas",
        category: "limpieza",
        description: "Limpieza especializada de ventanas y vidrios",
        icon: "🪟",
        priceRange: { min: 3000, max: 8000, unit: "ventana" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: false,
        requirements: ["Equipo especializado", "Productos para vidrios"],
        tags: ["ventanas", "vidrios", "especializada"]
      },
      {
        id: "limpieza-alfombras",
        name: "Limpieza de Alfombras",
        category: "limpieza",
        description: "Lavado y limpieza profunda de alfombras y tapetes",
        icon: "🧽",
        priceRange: { min: 15000, max: 35000, unit: "m2" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: false,
        requirements: ["Máquina especializada", "Productos específicos"],
        tags: ["alfombras", "tapetes", "lavado"]
      },
      {
        id: "limpieza-post-construccion",
        name: "Limpieza Post-Construcción",
        category: "limpieza",
        description: "Limpieza después de obras y remodelaciones",
        icon: "🏗️",
        priceRange: { min: 120000, max: 300000, unit: "servicio" },
        duration: { min: 6, max: 12, unit: "horas" },
        popular: false,
        requirements: ["Equipo industrial", "Productos especializados"],
        tags: ["post-construcción", "obras", "industrial"]
      },
      {
        id: "desinfeccion",
        name: "Desinfección",
        category: "limpieza",
        description: "Desinfección profunda de espacios",
        icon: "🦠",
        priceRange: { min: 50000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Productos desinfectantes", "Equipo de protección"],
        tags: ["desinfección", "sanitización", "covid"]
      }
    ]
  },
  {
    id: "plomeria",
    name: "Plomería",
    icon: "🔧",
    color: "#34C759",
    description: "Reparaciones e instalaciones de plomería",
    services: [
      {
        id: "destape-tuberia",
        name: "Destape de Tubería",
        category: "plomeria",
        description: "Destape de tuberías y desagües obstruidos",
        icon: "🚿",
        priceRange: { min: 40000, max: 80000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Herramientas especializadas"],
        tags: ["destape", "tubería", "desagüe"]
      },
      {
        id: "reparacion-grifo",
        name: "Reparación de Grifo",
        category: "plomeria",
        description: "Reparación de grifos que gotean o no funcionan correctamente",
        icon: "🚰",
        priceRange: { min: 25000, max: 50000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Repuestos básicos incluidos"],
        tags: ["grifo", "reparación", "goteo"]
      },
      {
        id: "instalacion-sanitario",
        name: "Instalación de Sanitario",
        category: "plomeria",
        description: "Instalación completa de sanitario nuevo",
        icon: "🚽",
        priceRange: { min: 60000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Sanitario no incluido", "Herramientas"],
        tags: ["instalación", "sanitario", "baño"]
      },
      {
        id: "reparacion-llave",
        name: "Reparación de Llaves",
        category: "plomeria",
        description: "Reparación de llaves que gotean o no funcionan",
        icon: "🚰",
        priceRange: { min: 25000, max: 50000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: false,
        requirements: ["Repuestos según necesidad"],
        tags: ["reparación", "llave", "goteo"]
      },
      {
        id: "instalacion-ducha",
        name: "Instalación de Ducha",
        category: "plomeria",
        description: "Instalación completa de ducha y grifería",
        icon: "🚿",
        priceRange: { min: 80000, max: 150000, unit: "servicio" },
        duration: { min: 3, max: 5, unit: "horas" },
        popular: true,
        requirements: ["Ducha no incluida", "Herramientas especializadas"],
        tags: ["instalación", "ducha", "grifería"]
      },
      {
        id: "reparacion-calentador",
        name: "Reparación de Calentador",
        category: "plomeria",
        description: "Mantenimiento y reparación de calentadores de agua",
        icon: "🔥",
        priceRange: { min: 50000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Repuestos según necesidad", "Técnico certificado"],
        tags: ["calentador", "reparación", "agua caliente"]
      }
    ]
  },
  {
    id: "electricidad",
    name: "Electricidad",
    icon: "⚡",
    color: "#FF9500",
    description: "Instalaciones y reparaciones eléctricas",
    services: [
      {
        id: "instalacion-toma",
        name: "Instalación de Toma",
        category: "electricidad",
        description: "Instalación de nuevas tomas eléctricas",
        icon: "🔌",
        priceRange: { min: 35000, max: 70000, unit: "toma" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Materiales incluidos", "Certificado"],
        tags: ["instalación", "toma", "enchufe"]
      },
      {
        id: "reparacion-corto",
        name: "Reparación de Cortocircuito",
        category: "electricidad",
        description: "Diagnóstico y reparación de problemas eléctricos",
        icon: "⚠️",
        priceRange: { min: 80000, max: 200000, unit: "servicio" },
        duration: { min: 2, max: 6, unit: "horas" },
        popular: true,
        requirements: ["Diagnóstico incluido", "Electricista certificado"],
        tags: ["reparación", "cortocircuito", "emergencia"]
      },
      {
        id: "instalacion-lampara",
        name: "Instalación de Lámparas",
        category: "electricidad",
        description: "Instalación de lámparas y sistemas de iluminación",
        icon: "💡",
        priceRange: { min: 25000, max: 60000, unit: "lámpara" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Lámpara no incluida", "Herramientas básicas"],
        tags: ["instalación", "lámpara", "iluminación"]
      },
      {
        id: "instalacion-ventilador",
        name: "Instalación de Ventilador",
        category: "electricidad",
        description: "Instalación de ventiladores de techo",
        icon: "🌀",
        priceRange: { min: 40000, max: 80000, unit: "ventilador" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Ventilador no incluido", "Soporte adecuado"],
        tags: ["instalación", "ventilador", "techo"]
      }
    ]
  },
  {
    id: "jardineria",
    name: "Jardinería",
    icon: "🌱",
    color: "#34C759",
    description: "Servicios de jardinería y paisajismo",
    services: [
      {
        id: "mantenimiento-jardin",
        name: "Mantenimiento de Jardín",
        category: "jardineria",
        description: "Poda, riego y mantenimiento general del jardín",
        icon: "🌿",
        priceRange: { min: 30000, max: 80000, unit: "servicio" },
        duration: { min: 2, max: 5, unit: "horas" },
        popular: true,
        requirements: ["Herramientas incluidas"],
        tags: ["jardín", "poda", "mantenimiento"]
      },
      {
        id: "corte-cesped",
        name: "Corte de Césped",
        category: "jardineria",
        description: "Corte y mantenimiento de césped",
        icon: "🌾",
        priceRange: { min: 20000, max: 50000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Cortadora incluida"],
        tags: ["césped", "corte", "jardín"]
      },
      {
        id: "siembra-plantas",
        name: "Siembra de Plantas",
        category: "jardineria",
        description: "Siembra y trasplante de plantas y flores",
        icon: "🌺",
        priceRange: { min: 15000, max: 40000, unit: "servicio" },
        duration: { min: 1, max: 4, unit: "horas" },
        popular: false,
        requirements: ["Plantas no incluidas", "Tierra y abono"],
        tags: ["siembra", "plantas", "flores"]
      }
    ]
  },
  {
    id: "cuidado",
    name: "Cuidado Personal",
    icon: "👥",
    color: "#8E8E93",
    description: "Servicios de cuidado y acompañamiento",
    services: [
      {
        id: "cuidado-adulto",
        name: "Cuidado de Adulto Mayor",
        category: "cuidado",
        description: "Acompañamiento y cuidado de adultos mayores",
        icon: "👴",
        priceRange: { min: 15000, max: 25000, unit: "hora" },
        duration: { min: 4, max: 12, unit: "horas" },
        popular: true,
        requirements: ["Experiencia certificada", "Referencias"],
        tags: ["adulto mayor", "cuidado", "acompañamiento"]
      },
      {
        id: "cuidado-ninos",
        name: "Cuidado de Niños",
        category: "cuidado",
        description: "Cuidado y entretenimiento de niños",
        icon: "👶",
        priceRange: { min: 12000, max: 20000, unit: "hora" },
        duration: { min: 2, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Experiencia con niños", "Referencias"],
        tags: ["niños", "cuidado", "niñera"]
      },
      {
        id: "cuidado-mascotas",
        name: "Cuidado de Mascotas",
        category: "cuidado",
        description: "Cuidado, paseo y alimentación de mascotas",
        icon: "🐕",
        priceRange: { min: 8000, max: 15000, unit: "hora" },
        duration: { min: 1, max: 4, unit: "horas" },
        popular: false,
        requirements: ["Amor por los animales"],
        tags: ["mascotas", "paseo", "cuidado"]
      }
    ]
  },
  {
    id: "cocina",
    name: "Cocina y Gastronomía",
    icon: "👨‍🍳",
    color: "#FF3B30",
    description: "Servicios de cocina y preparación de alimentos",
    services: [
      {
        id: "chef-domicilio",
        name: "Chef a Domicilio",
        category: "cocina",
        description: "Chef profesional para eventos y ocasiones especiales",
        icon: "🍽️",
        priceRange: { min: 80000, max: 200000, unit: "evento" },
        duration: { min: 3, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Ingredientes incluidos", "Chef certificado"],
        tags: ["chef", "cocina", "eventos"]
      },
      {
        id: "preparacion-comidas",
        name: "Preparación de Comidas",
        category: "cocina",
        description: "Preparación de comidas caseras para la semana",
        icon: "🥘",
        priceRange: { min: 50000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 6, unit: "horas" },
        popular: true,
        requirements: ["Ingredientes no incluidos", "Cocina disponible"],
        tags: ["comidas", "casera", "semanal"]
      },
      {
        id: "catering-eventos",
        name: "Catering para Eventos",
        category: "cocina",
        description: "Servicio de catering para eventos y celebraciones",
        icon: "🎂",
        priceRange: { min: 15000, max: 35000, unit: "persona" },
        duration: { min: 4, max: 10, unit: "horas" },
        popular: false,
        requirements: ["Menú personalizado", "Servicio completo"],
        tags: ["catering", "eventos", "celebraciones"]
      }
    ]
  },
  {
    id: "tecnologia",
    name: "Tecnología",
    icon: "💻",
    color: "#5856D6",
    description: "Servicios técnicos y de tecnología",
    services: [
      {
        id: "reparacion-pc",
        name: "Reparación de PC",
        category: "tecnologia",
        description: "Diagnóstico y reparación de computadores",
        icon: "🖥️",
        priceRange: { min: 50000, max: 150000, unit: "servicio" },
        duration: { min: 2, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Diagnóstico incluido"],
        tags: ["computador", "reparación", "técnico"]
      },
      {
        id: "instalacion-software",
        name: "Instalación de Software",
        category: "tecnologia",
        description: "Instalación y configuración de programas",
        icon: "💿",
        priceRange: { min: 25000, max: 60000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: false,
        requirements: ["Software original"],
        tags: ["software", "instalación", "configuración"]
      },
      {
        id: "reparacion-celulares",
        name: "Reparación de Celulares",
        category: "tecnologia",
        description: "Reparación de smartphones y tablets",
        icon: "📱",
        priceRange: { min: 40000, max: 200000, unit: "servicio" },
        duration: { min: 1, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Repuestos originales", "Garantía"],
        tags: ["celular", "smartphone", "tablet"]
      },
      {
        id: "configuracion-redes",
        name: "Configuración de Redes",
        category: "tecnologia",
        description: "Instalación y configuración de redes WiFi",
        icon: "📶",
        priceRange: { min: 60000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: true,
        requirements: ["Equipos de red"],
        tags: ["wifi", "redes", "internet"]
      },
      {
        id: "soporte-tecnico",
        name: "Soporte Técnico",
        category: "tecnologia",
        description: "Asistencia técnica remota y presencial",
        icon: "🛠️",
        priceRange: { min: 30000, max: 80000, unit: "hora" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Conocimientos técnicos"],
        tags: ["soporte", "asistencia", "técnico"]
      },
      {
        id: "recuperacion-datos",
        name: "Recuperación de Datos",
        category: "tecnologia",
        description: "Recuperación de archivos perdidos o dañados",
        icon: "💾",
        priceRange: { min: 80000, max: 250000, unit: "servicio" },
        duration: { min: 2, max: 8, unit: "horas" },
        popular: false,
        requirements: ["Software especializado"],
        tags: ["datos", "recuperación", "archivos"]
      }
    ]
  },
  {
    id: "belleza",
    name: "Belleza y Estética",
    icon: "💄",
    color: "#FF2D92",
    description: "Servicios de belleza y cuidado personal",
    services: [
      {
        id: "maquillaje-eventos",
        name: "Maquillaje para Eventos",
        category: "belleza",
        description: "Maquillaje profesional para bodas y eventos especiales",
        icon: "💋",
        priceRange: { min: 60000, max: 150000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Productos incluidos", "Maquilladora certificada"],
        tags: ["maquillaje", "eventos", "bodas"]
      },
      {
        id: "manicure-pedicure",
        name: "Manicure y Pedicure",
        category: "belleza",
        description: "Servicio completo de manicure y pedicure a domicilio",
        icon: "💅",
        priceRange: { min: 25000, max: 60000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Productos incluidos", "Kit esterilizado"],
        tags: ["manicure", "pedicure", "uñas"]
      },
      {
        id: "peinado-eventos",
        name: "Peinado para Eventos",
        category: "belleza",
        description: "Peinados profesionales para ocasiones especiales",
        icon: "💇‍♀️",
        priceRange: { min: 40000, max: 100000, unit: "servicio" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: false,
        requirements: ["Productos incluidos", "Estilista profesional"],
        tags: ["peinado", "eventos", "estilista"]
      }
    ]
  },
  {
    id: "mudanza",
    name: "Mudanza y Transporte",
    icon: "📦",
    color: "#8E8E93",
    description: "Servicios de mudanza y transporte",
    services: [
      {
        id: "mudanza-local",
        name: "Mudanza Local",
        category: "mudanza",
        description: "Mudanza dentro de la misma ciudad",
        icon: "🚚",
        priceRange: { min: 150000, max: 400000, unit: "servicio" },
        duration: { min: 4, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Vehículo incluido", "Ayudantes"],
        tags: ["mudanza", "local", "transporte"]
      },
      {
        id: "transporte-muebles",
        name: "Transporte de Muebles",
        category: "mudanza",
        description: "Transporte de muebles y objetos grandes",
        icon: "🛋️",
        priceRange: { min: 80000, max: 200000, unit: "servicio" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: false,
        requirements: ["Vehículo adecuado"],
        tags: ["transporte", "muebles", "carga"]
      }
    ]
  },
  {
    id: "construccion",
    name: "Construcción",
    icon: "🏗️",
    color: "#F59E0B",
    description: "Servicios de construcción y remodelación",
    services: [
      {
        id: "albanileria",
        name: "Albañilería",
        category: "construccion",
        description: "Trabajos de construcción y mampostería",
        icon: "🧱",
        priceRange: { min: 80000, max: 150000, unit: "día" },
        duration: { min: 1, max: 5, unit: "días" },
        popular: true,
        requirements: ["Herramientas propias", "Experiencia comprobable"],
        tags: ["construcción", "mampostería", "reparaciones"]
      },
      {
        id: "pintura",
        name: "Pintura",
        category: "construccion",
        description: "Pintura de interiores y exteriores",
        icon: "🎨",
        priceRange: { min: 15000, max: 25000, unit: "m2" },
        duration: { min: 1, max: 3, unit: "días" },
        popular: true,
        requirements: ["Materiales incluidos"],
        tags: ["pintura", "decoración", "remodelación"]
      },
      {
        id: "soldadura",
        name: "Soldadura",
        category: "construccion",
        description: "Trabajos de soldadura y herrería",
        icon: "⚒️",
        priceRange: { min: 60000, max: 120000, unit: "servicio" },
        duration: { min: 2, max: 6, unit: "horas" },
        popular: false,
        requirements: ["Equipo especializado", "Certificación"],
        tags: ["soldadura", "herrería", "metal"]
      },
      {
        id: "carpinteria",
        name: "Carpintería",
        category: "construccion",
        description: "Trabajos en madera y muebles",
        icon: "🪚",
        priceRange: { min: 70000, max: 200000, unit: "servicio" },
        duration: { min: 4, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Herramientas especializadas"],
        tags: ["carpintería", "madera", "muebles"]
      },
      {
        id: "ceramica",
        name: "Cerámica y Enchapes",
        category: "construccion",
        description: "Instalación de pisos y enchapes",
        icon: "🏺",
        priceRange: { min: 25000, max: 45000, unit: "m2" },
        duration: { min: 1, max: 4, unit: "días" },
        popular: true,
        requirements: ["Materiales por separado"],
        tags: ["cerámica", "pisos", "enchapes"]
      }
    ]
  },
  {
    id: "automotriz",
    name: "Automotriz",
    icon: "🚗",
    color: "#DC2626",
    description: "Servicios para vehículos",
    services: [
      {
        id: "mecanica",
        name: "Mecánica Automotriz",
        category: "automotriz",
        description: "Reparación y mantenimiento de vehículos",
        icon: "🔧",
        priceRange: { min: 50000, max: 300000, unit: "servicio" },
        duration: { min: 1, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Herramientas especializadas", "Experiencia"],
        tags: ["mecánica", "autos", "reparación"]
      },
      {
        id: "lavado-autos",
        name: "Lavado de Autos",
        category: "automotriz",
        description: "Lavado y detallado de vehículos",
        icon: "🧽",
        priceRange: { min: 15000, max: 80000, unit: "servicio" },
        duration: { min: 1, max: 3, unit: "horas" },
        popular: true,
        requirements: ["Productos de limpieza"],
        tags: ["lavado", "detallado", "limpieza"]
      },
      {
        id: "neumaticos",
        name: "Neumáticos",
        category: "automotriz",
        description: "Cambio y reparación de llantas",
        icon: "🛞",
        priceRange: { min: 20000, max: 50000, unit: "servicio" },
        duration: { min: 30, max: 90, unit: "minutos" },
        popular: false,
        requirements: ["Herramientas básicas"],
        tags: ["llantas", "neumáticos", "reparación"]
      }
    ]
  },
  {
    id: "educacion",
    name: "Educación",
    icon: "📚",
    color: "#059669",
    description: "Servicios educativos y tutorías",
    services: [
      {
        id: "clases-particulares",
        name: "Clases Particulares",
        category: "educacion",
        description: "Tutorías personalizadas en diversas materias",
        icon: "👨‍🏫",
        priceRange: { min: 25000, max: 60000, unit: "hora" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Conocimiento especializado"],
        tags: ["educación", "tutorías", "clases"]
      },
      {
        id: "idiomas",
        name: "Enseñanza de Idiomas",
        category: "educacion",
        description: "Clases de inglés, francés y otros idiomas",
        icon: "🗣️",
        priceRange: { min: 30000, max: 70000, unit: "hora" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: true,
        requirements: ["Certificación en idioma"],
        tags: ["idiomas", "inglés", "enseñanza"]
      },
      {
        id: "musica",
        name: "Clases de Música",
        category: "educacion",
        description: "Enseñanza de instrumentos musicales",
        icon: "🎵",
        priceRange: { min: 35000, max: 80000, unit: "hora" },
        duration: { min: 1, max: 2, unit: "horas" },
        popular: false,
        requirements: ["Instrumento propio"],
        tags: ["música", "instrumentos", "arte"]
      }
    ]
  },
  {
    id: "eventos",
    name: "Eventos",
    icon: "🎉",
    color: "#7C3AED",
    description: "Organización y servicios para eventos",
    services: [
      {
        id: "fotografia",
        name: "Fotografía",
        category: "eventos",
        description: "Fotografía para eventos y ocasiones especiales",
        icon: "📸",
        priceRange: { min: 200000, max: 800000, unit: "evento" },
        duration: { min: 2, max: 8, unit: "horas" },
        popular: true,
        requirements: ["Equipo profesional"],
        tags: ["fotografía", "eventos", "bodas"]
      },
      {
        id: "catering",
        name: "Catering",
        category: "eventos",
        description: "Servicio de comida para eventos",
        icon: "🍽️",
        priceRange: { min: 15000, max: 35000, unit: "persona" },
        duration: { min: 4, max: 12, unit: "horas" },
        popular: true,
        requirements: ["Permisos sanitarios"],
        tags: ["catering", "comida", "eventos"]
      },
      {
        id: "animacion",
        name: "Animación",
        category: "eventos",
        description: "Animación para fiestas infantiles y eventos",
        icon: "🤡",
        priceRange: { min: 80000, max: 200000, unit: "evento" },
        duration: { min: 2, max: 4, unit: "horas" },
        popular: false,
        requirements: ["Vestuario y materiales"],
        tags: ["animación", "fiestas", "niños"]
      }
    ]
  }
]

export function ServicesProvider({ children }: { children: React.ReactNode }) {
  const [categories] = useState<ServiceCategory[]>(serviceCategories)

  const getServicesByCategory = (categoryId: string): Service[] => {
    const category = categories.find(cat => cat.id === categoryId)
    return category?.services || []
  }

  const getServiceById = (serviceId: string): Service | undefined => {
    for (const category of categories) {
      const service = category.services.find(service => service.id === serviceId)
      if (service) return service
    }
    return undefined
  }

  const searchServices = (query: string): Service[] => {
    const results: Service[] = []
    const searchTerm = query.toLowerCase()
    
    categories.forEach(category => {
      category.services.forEach(service => {
        if (
          service.name.toLowerCase().includes(searchTerm) ||
          service.description.toLowerCase().includes(searchTerm) ||
          service.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        ) {
          results.push(service)
        }
      })
    })
    
    return results
  }

  const getPopularServices = (): Service[] => {
    const popular: Service[] = []
    categories.forEach(category => {
      category.services.forEach(service => {
        if (service.popular) {
          popular.push(service)
        }
      })
    })
    return popular
  }

  const getAllServices = (): Service[] => {
    const allServices: Service[] = []
    categories.forEach(category => {
      category.services.forEach(service => {
        allServices.push(service)
      })
    })
    return allServices
  }

  return (
    <ServicesContext.Provider
      value={{
        categories,
        getServicesByCategory,
        getServiceById,
        searchServices,
        getPopularServices,
        getAllServices,
      }}
    >
      {children}
    </ServicesContext.Provider>
  )
}

export function useServices() {
  const context = useContext(ServicesContext)
  if (context === undefined) {
    throw new Error("useServices must be used within a ServicesProvider")
  }
  return context
}
