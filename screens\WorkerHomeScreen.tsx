"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Switch } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useGoals } from "../contexts/GoalsContext"

export default function WorkerHomeScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { goals } = useGoals()
  const [isLive, setIsLive] = useState(false)
  const [isAvailable, setIsAvailable] = useState(true)

  const toggleLive = () => {
    if (!isLive) {
      Alert.alert("Iniciar Transmisión", "¿Quieres comenzar a transmitir en vivo tu trabajo?", [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Iniciar",
          onPress: () => {
            setIsLive(true)
            navigation.navigate("Live", { isStreaming: true })
          },
        },
      ])
    } else {
      setIsLive(false)
    }
  }

  const mockTasks = [
    { id: "1", title: "Reparar grifo de cocina", location: "Zona Norte", payment: "$45", urgent: true },
    { id: "2", title: "Limpieza profunda apartamento", location: "Centro", payment: "$80", urgent: false },
    { id: "3", title: "Instalación de lámpara", location: "Zona Sur", payment: "$35", urgent: false },
  ]

  const currentGoal = goals.find((g) => g.type === "income")
  const goalProgress = currentGoal ? (currentGoal.currentAmount / currentGoal.targetAmount) * 100 : 0

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.greeting, { color: colors.text }]}>¡Hola, {user?.name}! 💪</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>Listo para trabajar hoy</Text>
      </View>

      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
          onPress={() => navigation.navigate("Goals")}
        >
          <Text style={[styles.actionIcon, { color: colors.textSecondary }]}>🎯</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Mis Metas</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
          onPress={() => navigation.navigate("Profile")}
        >
          <Text style={[styles.actionIcon, { color: colors.textSecondary }]}>👤</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Perfil</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
          onPress={() => navigation.navigate("Calendar")}
        >
          <Text style={[styles.actionIcon, { color: colors.textSecondary }]}>📅</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Calendario</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Estado de Trabajo</Text>

        <View style={styles.statusRow}>
          <Text style={[styles.statusLabel, { color: colors.text }]}>Disponible para trabajar</Text>
          <Switch
            value={isAvailable}
            onValueChange={setIsAvailable}
            trackColor={{ false: colors.border, true: colors.primary }}
            thumbColor={isAvailable ? "#FFFFFF" : colors.textSecondary}
          />
        </View>

        <TouchableOpacity
          style={[styles.liveToggle, { backgroundColor: isLive ? colors.primary : colors.surface, borderColor: colors.border }]}
          onPress={toggleLive}
        >
          <Text style={[styles.liveToggleText, { color: isLive ? "#FFFFFF" : colors.text }]}>
            {isLive ? "● EN VIVO" : "Iniciar Transmisión"}
          </Text>
        </TouchableOpacity>
      </View>

      {currentGoal && (
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Meta del Mes</Text>
          <Text style={[styles.goalTitle, { color: colors.text }]}>{currentGoal.title}</Text>
          <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
            <View
              style={[
                styles.progressFill,
                { backgroundColor: colors.success, width: `${Math.min(goalProgress, 100)}%` },
              ]}
            />
          </View>
          <Text style={[styles.progressText, { color: colors.textSecondary }]}>
            ${currentGoal.currentAmount} / ${currentGoal.targetAmount} ({goalProgress.toFixed(0)}%)
          </Text>
        </View>
      )}

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Tareas Disponibles</Text>
        {mockTasks.map((task) => (
          <TouchableOpacity
            key={task.id}
            style={[styles.taskCard, { backgroundColor: colors.background, borderColor: colors.border }]}
            onPress={() =>
              Alert.alert("Tarea", "¿Quieres aceptar esta tarea?", [
                { text: "Cancelar", style: "cancel" },
                {
                  text: "Aceptar",
                  onPress: () => Alert.alert("¡Genial!", "Tarea aceptada. El cliente será notificado."),
                },
              ])
            }
          >
            <View style={styles.taskHeader}>
              <Text style={[styles.taskTitle, { color: colors.text }]}>{task.title}</Text>
              {task.urgent && (
                <View style={[styles.urgentBadge, { borderColor: "#FF3B30" }]}>
                  <Text style={[styles.urgentText, { color: "#FF3B30" }]}>URGENTE</Text>
                </View>
              )}
            </View>
            <Text style={[styles.taskLocation, { color: colors.textSecondary }]}>{task.location}</Text>
            <Text style={[styles.taskPayment, { color: colors.success }]}>💰 {task.payment}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Estadísticas del Mes</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>12</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Tareas Completadas</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.success }]}>$480</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Ganado</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.warning }]}>4.8</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calificación</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
  },
  quickActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  actionButton: {
    alignItems: "center",
    padding: 15,
    borderRadius: 12,
    minWidth: 80,
    borderWidth: 1,
  },
  actionIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  actionText: {
    fontSize: 12,
    fontWeight: "600",
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  statusRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  statusLabel: {
    fontSize: 16,
  },
  liveToggle: {
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    borderWidth: 1,
  },
  liveToggleText: {
    fontSize: 16,
    fontWeight: "600",
  },
  goalTitle: {
    fontSize: 16,
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    textAlign: "center",
  },
  taskCard: {
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    marginBottom: 10,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  urgentBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  urgentText: {
    fontSize: 10,
    fontWeight: "600",
  },
  taskLocation: {
    fontSize: 14,
    marginBottom: 4,
  },
  taskPayment: {
    fontSize: 16,
    fontWeight: "bold",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
})
