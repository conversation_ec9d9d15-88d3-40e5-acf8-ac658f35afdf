"use client"

import { useState } from "react"
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native"
import * as LocalAuthentication from "expo-local-authentication"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function LoginScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { login } = useUser()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const clearStorage = async () => {
    try {
      await AsyncStorage.clear()
      Alert.alert("Almacenamiento Limpiado", "El almacenamiento ha sido limpiado. Por favor reinicia la aplicación.")
    } catch (error) {
      console.error("Error limpiando almacenamiento:", error)
    }
  }

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert("Error", "Por favor completa todos los campos")
      return
    }

    setIsLoading(true)
    const success = await login(email, password)
    setIsLoading(false)

    if (!success) {
      Alert.alert("Error", "Credenciales incorrectas")
    }
  }

  const handleBiometricLogin = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync()
      if (!hasHardware) {
        Alert.alert("Error", "Tu dispositivo no soporta autenticación biométrica")
        return
      }

      const isEnrolled = await LocalAuthentication.isEnrolledAsync()
      if (!isEnrolled) {
        Alert.alert("Error", "No tienes configurada la autenticación biométrica")
        return
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: "Inicia sesión con tu huella o rostro",
        cancelLabel: "Cancelar",
        fallbackLabel: "Usar contraseña",
      })

      if (result.success) {
        // Simulate login with saved credentials
        await login("<EMAIL>", "password")
      }
    } catch (error) {
      Alert.alert("Error", "Error en la autenticación biométrica")
    }
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.logo}>🚀</Text>
          <Text style={[styles.title, { color: colors.text }]}>TaskApp LATAM</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>Bienvenido de vuelta</Text>
        </View>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Correo electrónico"
            placeholderTextColor={colors.textSecondary}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Contraseña"
            placeholderTextColor={colors.textSecondary}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />

          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: colors.primary }]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            <Text style={styles.loginButtonText}>{isLoading ? "Iniciando sesión..." : "Iniciar Sesión"}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.biometricButton, { borderColor: colors.primary }]}
            onPress={handleBiometricLogin}
          >
            <Text style={[styles.biometricButtonText, { color: colors.primary }]}>🔒 Usar Huella/FaceID</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.registerLink} onPress={() => navigation.navigate("Register")}>
            <Text style={[styles.registerLinkText, { color: colors.textSecondary }]}>
              ¿No tienes cuenta? <Text style={{ color: colors.primary }}>Regístrate aquí</Text>
            </Text>
          </TouchableOpacity>

          {/* Botón temporal para limpiar storage */}
          <TouchableOpacity
            style={[styles.clearButton, { backgroundColor: colors.error }]}
            onPress={clearStorage}
          >
            <Text style={styles.clearButtonText}>
              🗑️ Clear Storage (Debug)
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginBottom: 40,
  },
  logo: {
    fontSize: 60,
    marginBottom: 10,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
  },
  form: {
    width: "100%",
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
  },
  loginButton: {
    height: 50,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
  },
  loginButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  biometricButton: {
    height: 50,
    borderWidth: 2,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  biometricButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  registerLink: {
    alignItems: "center",
  },
  registerLinkText: {
    fontSize: 14,
  },
  clearButton: {
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  clearButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
})
