"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Linking } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useTasks } from "../contexts/TaskContext"

export default function TaskDetailScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { getTaskById, assignTask } = useTasks()
  const { taskId } = route.params
  const [showFullDescription, setShowFullDescription] = useState(false)

  const task = getTaskById(taskId)

  if (!task) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.errorText, { color: colors.text }]}>Tarea no encontrada</Text>
      </View>
    )
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "#FF3B30"
      case "medium":
        return "#FF9500"
      case "low":
        return "#34C759"
      default:
        return colors.textSecondary
    }
  }

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "Urgente"
      case "medium":
        return "Moderado"
      case "low":
        return "Flexible"
      default:
        return ""
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return colors.success
      case "assigned":
        return colors.warning
      case "in_progress":
        return colors.primary
      case "completed":
        return colors.success
      case "cancelled":
        return colors.error
      default:
        return colors.textSecondary
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "Disponible"
      case "assigned":
        return "Asignada"
      case "in_progress":
        return "En Progreso"
      case "completed":
        return "Completada"
      case "cancelled":
        return "Cancelada"
      default:
        return ""
    }
  }

  const handleApplyForTask = () => {
    Alert.alert("Aplicar a Tarea", "¿Estás seguro que quieres aplicar a esta tarea?", [
      { text: "Cancelar", style: "cancel" },
      {
        text: "Aplicar",
        onPress: async () => {
          await assignTask(task.id, "current_worker")
          Alert.alert("¡Aplicación Enviada!", "El cliente será notificado de tu interés en la tarea.")
        },
      },
    ])
  }

  const handleContactClient = () => {
    navigation.navigate("Chat", { taskId: task.id, clientId: task.clientId })
  }

  const handleOpenMap = () => {
    Alert.alert("Abrir Mapa", "¿Cómo quieres ver la ubicación?", [
      { text: "Cancelar", style: "cancel" },
      {
        text: "Mapa Interno",
        onPress: () => navigation.navigate("Map", {
          taskId: task.id,
          address: task.location.address,
          coordinates: task.location.coordinates
        }),
      },
      {
        text: "Google Maps",
        onPress: () => {
          const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(task.location.address)}`
          Linking.openURL(url)
        },
      },
    ])
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.headerTop}>
          <Text style={[styles.title, { color: colors.text }]}>{task.title}</Text>
          <View style={styles.statusBadge}>
            <Text style={[styles.statusText, { color: getStatusColor(task.status) }]}>{getStatusText(task.status)}</Text>
          </View>
        </View>

        <View style={styles.headerInfo}>
          <View style={styles.infoItem}>
            <Text style={styles.infoIcon}>📍</Text>
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>{task.category}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoIcon}>⏱️</Text>
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>~{task.estimatedDuration}h</Text>
          </View>
          <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(task.urgency) }]}>
            <Text style={styles.urgencyText}>{getUrgencyText(task.urgency)}</Text>
          </View>
        </View>
      </View>

      {/* Budget */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Presupuesto</Text>
        <View style={styles.budgetContainer}>
          <Text style={[styles.budgetAmount, { color: colors.success }]}>
            ${task.budget.min} - ${task.budget.max}
          </Text>
          <Text style={[styles.budgetNote, { color: colors.textSecondary }]}>Precio negociable</Text>
        </View>
      </View>

      {/* Description */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Descripción</Text>
        <Text style={[styles.description, { color: colors.text }]} numberOfLines={showFullDescription ? undefined : 3}>
          {task.description}
        </Text>
        {task.description.length > 150 && (
          <TouchableOpacity onPress={() => setShowFullDescription(!showFullDescription)}>
            <Text style={[styles.readMoreText, { color: colors.primary }]}>
              {showFullDescription ? "Ver menos" : "Ver más"}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Location */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Ubicación</Text>
          <TouchableOpacity style={[styles.mapButton, { backgroundColor: colors.primary }]} onPress={handleOpenMap}>
            <Text style={styles.mapButtonText}>Ver Mapa</Text>
          </TouchableOpacity>
        </View>
        <Text style={[styles.address, { color: colors.textSecondary }]}>{task.location.address}</Text>
      </View>

      {/* Requirements */}
      {task.requirements.length > 0 && (
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Requisitos</Text>
          {task.requirements.map((requirement, index) => (
            <View key={index} style={styles.requirementItem}>
              <Text style={styles.requirementBullet}>•</Text>
              <Text style={[styles.requirementText, { color: colors.text }]}>{requirement}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Schedule */}
      {task.scheduledFor && (
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Fecha Programada</Text>
          <Text style={[styles.scheduleText, { color: colors.text }]}>
            {task.scheduledFor.toLocaleDateString("es-ES", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </Text>
        </View>
      )}



      {/* Actions */}
      {user?.role === "worker" && task.status === "open" && (
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.secondaryButton, { backgroundColor: colors.background, borderColor: colors.primary }]}
            onPress={handleContactClient}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.primary }]}>Contactar Cliente</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={handleApplyForTask}
          >
            <Text style={styles.primaryButtonText}>Aplicar a esta Tarea</Text>
          </TouchableOpacity>
        </View>
      )}

      {user?.role === "client" && task.clientId === "current_user" && (
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.secondaryButton, { backgroundColor: colors.background, borderColor: colors.warning }]}
            onPress={() => Alert.alert("Función", "Editar tarea (próximamente)")}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.warning }]}>Editar Tarea</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryButton, { backgroundColor: colors.background, borderColor: colors.error }]}
            onPress={() => Alert.alert("Función", "Cancelar tarea (próximamente)")}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.error }]}>Cancelar Tarea</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 50,
  },
  header: {
    padding: 20,
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    flex: 1,
    marginRight: 15,
  },
  statusBadge: {
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  headerInfo: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  infoText: {
    fontSize: 14,
  },
  urgencyBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  urgencyText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  budgetContainer: {
    alignItems: "center",
  },
  budgetAmount: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  budgetNote: {
    fontSize: 14,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
  },
  readMoreText: {
    fontSize: 14,
    fontWeight: "600",
    marginTop: 10,
  },
  mapButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  mapButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  address: {
    fontSize: 16,
    lineHeight: 22,
  },
  requirementItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  requirementBullet: {
    fontSize: 16,
    marginRight: 10,
    marginTop: 2,
  },
  requirementText: {
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
  },
  scheduleText: {
    fontSize: 16,
    textTransform: "capitalize",
  },

  actionsContainer: {
    padding: 20,
    gap: 15,
  },
  primaryButton: {
    padding: 18,
    borderRadius: 15,
    alignItems: "center",
  },
  primaryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  secondaryButton: {
    padding: 18,
    borderRadius: 15,
    alignItems: "center",
    borderWidth: 2,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
})
