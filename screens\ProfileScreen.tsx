import React, { useState } from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import ProfileImagePicker from '../components/ProfileImagePicker'
import ProfileModal from '../components/ProfileModal'
import { useTheme } from '../contexts/ThemeContext'
import { useUser } from '../contexts/UserContext'
import RoleSwitcher from '../components/RoleSwitcher'

interface MenuItem {
  id: string
  title: string
  icon: string
  action?: () => void
  isDestructive?: boolean
  isHighlighted?: boolean
}

export default function ProfileScreen({ navigation }: any) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, logout, updateUser } = useUser()
  const [expandedSections, setExpandedSections] = useState<string[]>([])
  const [profileModalVisible, setProfileModalVisible] = useState(false)

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const openProfileModal = () => {
    setProfileModalVisible(true)
  }

  const closeProfileModal = () => {
    setProfileModalVisible(false)
  }

  const handleImageSelected = (imageUri: string) => {
    updateUser({ avatar: imageUri })
    Alert.alert('Éxito', 'Foto de perfil actualizada correctamente')
  }

  const handleLogout = () => {
    Alert.alert(
      "Cerrar Sesión",
      "¿Estás seguro que quieres cerrar sesión?",
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Cerrar Sesión", style: "destructive", onPress: logout }
      ]
    )
  }

  const accountItems: MenuItem[] = [
    {
      id: 'account-details',
      title: 'Detalles de cuenta',
      icon: 'person-outline',
      action: () => {
        Alert.alert('Detalles de cuenta', 'Nombre: ' + (user?.name || 'Usuario') + '\nEmail: ' + (user?.email || 'No disponible') + '\nTeléfono: ' + (user?.phone || 'No disponible'))
      }
    },
    {
      id: 'tasker-profile',
      title: 'Perfil de trabajador',
      icon: 'briefcase-outline',
      action: () => {
        Alert.alert('Perfil de trabajador', 'Habilidades: ' + (user?.skills?.join(', ') || 'No definidas') + '\nExperiencia: ' + (user?.experience || 'No definida'))
      }
    },
    {
      id: 'payments',
      title: 'Pagos y facturación',
      icon: 'card-outline',
      action: () => {
        Alert.alert('Pagos y facturación', 'Métodos de pago:\n• Tarjeta de crédito\n• Transferencia bancaria\n• Efectivo\n\nHistorial de pagos disponible en el dashboard.')
      }
    },
    {
      id: 'reviews',
      title: 'Reseñas y calificaciones',
      icon: 'star-outline',
      action: () => {
        Alert.alert('Reseñas y calificaciones', 'Calificación promedio: 4.8/5\nTotal de reseñas: 127\nÚltima reseña: "Excelente trabajo, muy profesional"')
      }
    },
    {
      id: 'invite-friends',
      title: 'Invita amigos, gana dinero',
      icon: 'gift-outline',
      action: () => {
        Alert.alert('Invitar Amigos', 'Comparte tu código: TASK' + (user?.id?.slice(-4) || '1234') + '\n\n¡Gana $50.000 por cada amigo que se registre y complete su primer trabajo!')
      },
    },
  ]

  const settingsItems: MenuItem[] = [
    {
      id: 'theme',
      title: `Tema ${theme === 'light' ? 'Oscuro' : 'Claro'}`,
      icon: theme === 'light' ? 'moon-outline' : 'sunny-outline',
      action: () => {
        toggleTheme()
        Alert.alert('Tema cambiado', `Cambiado a tema ${theme === 'light' ? 'oscuro' : 'claro'}`)
      }
    },
    {
      id: 'notifications',
      title: 'Notificaciones',
      icon: 'notifications-outline',
      action: () => {
        Alert.alert('Configuración de Notificaciones', 'Notificaciones push: Activadas\nEmail: Activado\nSMS: Desactivado\n\nPuedes cambiar estas configuraciones en ajustes del sistema.')
      }
    },
    {
      id: 'privacy',
      title: 'Privacidad y seguridad',
      icon: 'shield-outline',
      action: () => {
        Alert.alert('Privacidad y Seguridad', 'Perfil público: Sí\nMostrar teléfono: No\nMostrar ubicación exacta: No\nVerificación en dos pasos: Activada')
      }
    },
    {
      id: 'language',
      title: 'Idioma',
      icon: 'language-outline',
      action: () => {
        Alert.alert('Configuración de Idioma', 'Idioma actual: Español (Colombia)\n\nIdiomas disponibles:\n• Español\n• English\n• Português')
      }
    },
    {
      id: 'help',
      title: 'Ayuda',
      icon: 'help-circle-outline',
      action: () => {
        Alert.alert('Centro de Ayuda', 'Preguntas frecuentes\nContactar soporte\nGuías de uso\nTutoriales en video\n\nEmail: <EMAIL>\nWhatsApp: +57 ************')
      }
    },
    {
      id: 'about',
      title: 'Acerca de TaskApp',
      icon: 'information-circle-outline',
      action: () => {
        Alert.alert('TaskApp LATAM', 'Versión: 1.0.0\nDesarrollado para LATAM\n\nConecta profesionales con clientes\nde manera rápida y segura.\n\n© 2024 TaskApp LATAM')
      }
    },
    {
      id: 'logout',
      title: 'Cerrar Sesión',
      icon: 'log-out-outline',
      action: handleLogout,
      isDestructive: true
    }
  ]



  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* User Profile Section - Clickeable para abrir modal */}
        <TouchableOpacity
          style={[styles.profileCard, { backgroundColor: colors.surface }]}
          onPress={openProfileModal}
          activeOpacity={0.8}
        >
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <ProfileImagePicker
                currentImage={user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'}
                onImageSelected={handleImageSelected}
                size={60}
              />
              {user?.isVerified && (
                <View style={styles.verifiedBadge}>
                  <Ionicons name="checkmark-circle" size={18} color="#1DA1F2" />
                </View>
              )}
            </View>

            <View style={styles.profileInfo}>
              <Text style={[styles.userName, { color: colors.text }]}>
                {user?.name || 'hhh'}
              </Text>
              <Text style={[styles.userRole, { color: colors.textSecondary }]}>
                {user?.currentRole === 'client' ? 'Cliente' : 'Profesional'}
              </Text>
              <Text style={[styles.userLocation, { color: colors.textLight }]}>
                {user?.location || 'hddf'}
              </Text>
            </View>

            <View style={styles.expandIcon}>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={colors.textSecondary}
              />
            </View>
          </View>

          {/* Stats - Siempre visibles */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>12</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Tareas creadas</Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>4.8</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Rating promedio</Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>2 min</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Tiempo respuesta</Text>
            </View>
          </View>
        </TouchableOpacity>



        {/* Role Switcher */}
        {user?.roles && user.roles.length > 1 && (
          <View style={styles.roleSwitcherContainer}>
            <RoleSwitcher />
          </View>
        )}

        {/* Menu profesional */}
        <View style={[styles.menuCard, { backgroundColor: colors.surface }]}>
          {/* Account Information */}
          <View style={styles.menuSection}>
            <Text style={[styles.menuSectionTitle, { color: colors.textSecondary }]}>
              CUENTA
            </Text>
            {accountItems.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.menuItem,
                  { backgroundColor: colors.surface },
                  index === accountItems.length - 1 && styles.lastMenuItem
                ]}
                onPress={item.action}
              >
                <View style={styles.menuItemLeft}>
                  <Ionicons name={item.icon as any} size={20} color="#000000" />
                  <Text style={[styles.menuItemText, { color: '#000000' }]}>
                    {item.title}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
              </TouchableOpacity>
            ))}
          </View>

          {/* Settings */}
          <View style={styles.menuSection}>
            <Text style={[styles.menuSectionTitle, { color: colors.textSecondary }]}>
              CONFIGURACIÓN
            </Text>
            {settingsItems.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.menuItem,
                  { backgroundColor: item.isDestructive ? colors.error + '08' : colors.surface },
                  index === settingsItems.length - 1 && styles.lastMenuItem
                ]}
                onPress={item.action}
              >
                <View style={styles.menuItemLeft}>
                  <Ionicons
                    name={item.icon as any}
                    size={20}
                    color={item.isDestructive ? colors.error : '#000000'}
                  />
                  <Text style={[
                    styles.menuItemText,
                    { color: item.isDestructive ? colors.error : '#000000' }
                  ]}>
                    {item.title}
                  </Text>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={16}
                  color={item.isDestructive ? colors.error : colors.textLight}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Profile Modal */}
      <ProfileModal
        visible={profileModalVisible}
        onClose={closeProfileModal}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  roleSwitcherContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },



  // Estilos del perfil profesional
  profileCard: {
    marginHorizontal: 0,
    marginVertical: 0,
    borderRadius: 0,
    padding: 20,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 14,
  },
  profileInfo: {
    flex: 1,
  },
  expandIcon: {
    padding: 4,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 2,
  },
  profileInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
  },
  verifiedIcon: {
    marginLeft: 8,
  },
  userRole: {
    fontSize: 13,
    fontWeight: '400',
    marginBottom: 2,
  },
  userLocation: {
    fontSize: 12,
    fontWeight: '400',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 0.5,
    borderTopColor: '#E5E5E5',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 2,
    color: '#000000',
  },
  statLabel: {
    fontSize: 11,
    textAlign: 'center',
    lineHeight: 14,
    fontWeight: '400',
  },
  statDivider: {
    width: 0.5,
    height: 32,
    marginHorizontal: 8,
    backgroundColor: '#E0E0E0',
  },

  // Perfil expandido
  expandedProfile: {
    marginHorizontal: 20,
    marginTop: -10,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  profileDetails: {
    gap: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailText: {
    fontSize: 15,
    fontWeight: '500',
    flex: 1,
  },

  // Menús profesionales
  menuCard: {
    marginHorizontal: 0,
    marginVertical: 8,
    borderRadius: 0,
    padding: 0,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  menuSection: {
    paddingVertical: 4,
  },
  menuSectionTitle: {
    fontSize: 11,
    fontWeight: '600',
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    paddingHorizontal: 20,
    paddingVertical: 8,
    paddingTop: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderBottomWidth: 0,
    marginHorizontal: 0,
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  menuItemText: {
    fontSize: 15,
    fontWeight: '400',
    flex: 1,
  },
})
