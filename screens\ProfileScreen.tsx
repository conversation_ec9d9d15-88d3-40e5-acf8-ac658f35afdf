import React from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'
import { useUser } from '../contexts/UserContext'
import RoleSwitcher from '../components/RoleSwitcher'

interface MenuItem {
  id: string
  title: string
  icon: string
  action?: () => void
  isDestructive?: boolean
  isHighlighted?: boolean
}

export default function ProfileScreen({ navigation }: any) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, logout } = useUser()

  const handleLogout = () => {
    Alert.alert(
      "Cerrar Sesión",
      "¿Estás seguro que quieres cerrar sesión?",
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Cerrar Sesión", style: "destructive", onPress: logout }
      ]
    )
  }

  const accountItems: MenuItem[] = [
    {
      id: 'account-details',
      title: 'Detalles de cuenta',
      icon: 'person-outline',
      action: () => navigation.navigate('EditProfile')
    },
    {
      id: 'tasker-profile',
      title: 'Perfil de trabajador',
      icon: 'briefcase-outline',
      action: () => navigation.navigate('Skills')
    },
    {
      id: 'payments',
      title: 'Pagos y facturación',
      icon: 'card-outline',
      action: () => console.log('Payments - Coming soon')
    },
    {
      id: 'invite-friends',
      title: 'Invita amigos, gana dinero',
      icon: 'gift-outline',
      action: () => console.log('Invite friends - Coming soon'),
      isHighlighted: true
    }
  ]

  const settingsItems: MenuItem[] = [
    {
      id: 'theme',
      title: `Tema ${theme === 'light' ? 'Oscuro' : 'Claro'}`,
      icon: theme === 'light' ? 'moon-outline' : 'sunny-outline',
      action: toggleTheme
    },
    {
      id: 'notifications',
      title: 'Notificaciones',
      icon: 'notifications-outline',
      action: () => console.log('Notifications - Coming soon')
    },
    {
      id: 'help',
      title: 'Ayuda',
      icon: 'help-circle-outline',
      action: () => navigation.navigate('Help')
    },
    {
      id: 'logout',
      title: 'Cerrar Sesión',
      icon: 'log-out-outline',
      action: handleLogout,
      isDestructive: true
    }
  ]

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.menuItem,
        { backgroundColor: colors.surface },
        item.isHighlighted && { backgroundColor: '#E3F2FD' }
      ]}
      onPress={item.action}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons 
          name={item.icon as any} 
          size={24} 
          color={item.isDestructive ? '#EF4444' : item.isHighlighted ? '#007AFF' : colors.text}
        />
        <Text style={[
          styles.menuItemText,
          { color: item.isDestructive ? '#EF4444' : item.isHighlighted ? '#007AFF' : colors.text }
        ]}>
          {item.title}
        </Text>
      </View>
      <Ionicons 
        name="chevron-forward" 
        size={20} 
        color={colors.textSecondary} 
      />
    </TouchableOpacity>
  )

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Perfil</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Role Switcher */}
        {user?.roles && user.roles.length > 1 && (
          <View style={styles.roleSwitcherContainer}>
            <RoleSwitcher />
          </View>
        )}

        {/* Account Information Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionHeader, { color: colors.textSecondary }]}>
            INFORMACIÓN DE CUENTA
          </Text>
          {accountItems.map(renderMenuItem)}
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionHeader, { color: colors.textSecondary }]}>
            CONFIGURACIÓN
          </Text>
          {settingsItems.map(renderMenuItem)}
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  roleSwitcherContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: 16,
    marginHorizontal: 20,
    textTransform: 'uppercase',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 16,
  },
})
