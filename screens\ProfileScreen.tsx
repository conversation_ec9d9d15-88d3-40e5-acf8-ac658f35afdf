import React, { useState } from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import ProfileImagePicker from '../components/ProfileImagePicker'
import { useTheme } from '../contexts/ThemeContext'
import { useUser } from '../contexts/UserContext'
import RoleSwitcher from '../components/RoleSwitcher'

interface MenuItem {
  id: string
  title: string
  icon: string
  action?: () => void
  isDestructive?: boolean
  isHighlighted?: boolean
}

export default function ProfileScreen({ navigation }: any) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, logout, updateUser } = useUser()
  const [expandedSections, setExpandedSections] = useState<string[]>([])

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const handleImageSelected = (imageUri: string) => {
    updateUser({ avatar: imageUri })
    Alert.alert('Éxito', 'Foto de perfil actualizada correctamente')
  }

  const handleLogout = () => {
    Alert.alert(
      "Cerrar Sesión",
      "¿Estás seguro que quieres cerrar sesión?",
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Cerrar Sesión", style: "destructive", onPress: logout }
      ]
    )
  }

  const accountItems: MenuItem[] = [
    {
      id: 'account-details',
      title: 'Detalles de cuenta',
      icon: 'person-outline',
      action: () => navigation.navigate('EditProfile')
    },
    {
      id: 'tasker-profile',
      title: 'Perfil de trabajador',
      icon: 'briefcase-outline',
      action: () => navigation.navigate('Skills')
    },
    {
      id: 'payments',
      title: 'Pagos y facturación',
      icon: 'card-outline',
      action: () => console.log('Payments - Coming soon')
    },
    {
      id: 'invite-friends',
      title: 'Invita amigos, gana dinero',
      icon: 'gift-outline',
      action: () => console.log('Invite friends - Coming soon'),
      isHighlighted: true
    }
  ]

  const settingsItems: MenuItem[] = [
    {
      id: 'theme',
      title: `Tema ${theme === 'light' ? 'Oscuro' : 'Claro'}`,
      icon: theme === 'light' ? 'moon-outline' : 'sunny-outline',
      action: toggleTheme
    },
    {
      id: 'notifications',
      title: 'Notificaciones',
      icon: 'notifications-outline',
      action: () => console.log('Notifications - Coming soon')
    },
    {
      id: 'help',
      title: 'Ayuda',
      icon: 'help-circle-outline',
      action: () => navigation.navigate('Help')
    },
    {
      id: 'logout',
      title: 'Cerrar Sesión',
      icon: 'log-out-outline',
      action: handleLogout,
      isDestructive: true
    }
  ]

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.menuItem,
        { backgroundColor: colors.surface },
        item.isHighlighted && { backgroundColor: '#E3F2FD' }
      ]}
      onPress={item.action}
    >
      <View style={styles.menuItemLeft}>
        <Ionicons 
          name={item.icon as any} 
          size={24} 
          color={item.isDestructive ? '#EF4444' : item.isHighlighted ? '#007AFF' : colors.text}
        />
        <Text style={[
          styles.menuItemText,
          { color: item.isDestructive ? '#EF4444' : item.isHighlighted ? '#007AFF' : colors.text }
        ]}>
          {item.title}
        </Text>
      </View>
      <Ionicons 
        name="chevron-forward" 
        size={20} 
        color={colors.textSecondary} 
      />
    </TouchableOpacity>
  )

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        <View style={[styles.profileSection, { backgroundColor: colors.surface }]}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <ProfileImagePicker
                currentImage={user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'}
                onImageSelected={handleImageSelected}
                size={80}
              />
              {user?.isVerified && (
                <View style={styles.verifiedBadge}>
                  <Ionicons name="checkmark-circle" size={20} color="#1DA1F2" />
                </View>
              )}
            </View>
            <View style={styles.profileInfo}>
              <View style={styles.nameRow}>
                <Text style={[styles.userName, { color: colors.text }]}>{user?.name}</Text>
                {user?.isVerified && (
                  <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" style={styles.verifiedIcon} />
                )}
              </View>
              <Text style={[styles.userRole, { color: colors.textSecondary }]}>
                {user?.currentRole === 'client' ? 'Cliente' : 'Profesional'}
              </Text>
              <Text style={[styles.userEmail, { color: colors.textSecondary }]}>{user?.email}</Text>
            </View>
          </View>

          {/* User Statistics */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>
                {user?.currentRole === 'client' ? '12' : '156'}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                {user?.currentRole === 'client' ? 'Tareas creadas' : 'Trabajos completados'}
              </Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>
                {user?.currentRole === 'client' ? '4.8' : '4.9'}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Rating promedio
              </Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>
                {user?.currentRole === 'client' ? '2 min' : '30 min'}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Tiempo respuesta
              </Text>
            </View>
          </View>
        </View>

        {/* Role Switcher */}
        {user?.roles && user.roles.length > 1 && (
          <View style={styles.roleSwitcherContainer}>
            <RoleSwitcher />
          </View>
        )}

        {/* Account Information Section */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.sectionHeaderButton, { backgroundColor: colors.surface }]}
            onPress={() => toggleSection('account')}
          >
            <Text style={[styles.sectionHeader, { color: colors.textSecondary }]}>
              INFORMACIÓN DE CUENTA
            </Text>
            <Ionicons
              name={expandedSections.includes('account') ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={colors.textSecondary}
            />
          </TouchableOpacity>
          {expandedSections.includes('account') && (
            <View style={styles.sectionContent}>
              {accountItems.map(renderMenuItem)}
            </View>
          )}
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.sectionHeaderButton, { backgroundColor: colors.surface }]}
            onPress={() => toggleSection('settings')}
          >
            <Text style={[styles.sectionHeader, { color: colors.textSecondary }]}>
              CONFIGURACIÓN
            </Text>
            <Ionicons
              name={expandedSections.includes('settings') ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={colors.textSecondary}
            />
          </TouchableOpacity>
          {expandedSections.includes('settings') && (
            <View style={styles.sectionContent}>
              {settingsItems.map(renderMenuItem)}
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  roleSwitcherContainer: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeaderButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  sectionContent: {
    marginTop: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 16,
  },

  // Nuevos estilos para perfil
  profileSection: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 2,
  },
  profileInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  verifiedIcon: {
    marginLeft: 8,
  },
  userRole: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  statDivider: {
    width: 1,
    height: 40,
    marginHorizontal: 10,
  },
})
