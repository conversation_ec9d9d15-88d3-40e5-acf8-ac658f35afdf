"use client"

import { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Modal,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useTasks } from "../contexts/TaskContext"
import { useServices } from "../contexts/ServicesContext"
import { CustomServicesStorage } from "../services/customServicesStorage"

export default function CreateTaskScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { createTask } = useTasks()
  const { categories, getAllServices } = useServices()
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "",
    service: "",
    address: "",
    minBudget: "",
    maxBudget: "",
    urgency: "medium" as "low" | "medium" | "high",
    estimatedDuration: "",
    requirements: "",
  })

  const [showDropdown, setShowDropdown] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)

  // Obtener todos los servicios
  const allServices = getAllServices()

  // Filtrar servicios por categoría y búsqueda
  const filteredServices = allServices.filter(service => {
    const matchesCategory = selectedCategory === "" || service.category === selectedCategory
    const matchesSearch = searchQuery === "" ||
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Función para guardar servicios personalizados
  const saveCustomService = async (serviceName: string) => {
    try {
      const customService = await CustomServicesStorage.saveCustomService(serviceName)

      console.log("Servicio personalizado agregado:", customService)
      Alert.alert(
        "¡Servicio agregado!",
        `"${serviceName}" ha sido agregado a nuestros servicios. ¡Gracias por ayudarnos a mejorar!`,
        [{ text: "OK" }]
      )

    } catch (error) {
      console.error("Error al guardar servicio personalizado:", error)
      Alert.alert("Error", "No se pudo agregar el servicio personalizado")
    }
  }

  // Función para manejar archivos adjuntos
  const handleAttachment = async (type: 'photo' | 'document' | 'file') => {
    try {
      switch (type) {
        case 'photo':
          Alert.alert("Próximamente", "Funcionalidad de cámara/galería en desarrollo")
          // TODO: Implementar ImagePicker
          // const result = await ImagePicker.launchImageLibraryAsync({...})
          break
        case 'document':
          Alert.alert("Próximamente", "Funcionalidad de documentos en desarrollo")
          // TODO: Implementar DocumentPicker para PDFs, Word, etc.
          break
        case 'file':
          Alert.alert("Próximamente", "Funcionalidad de archivos en desarrollo")
          // TODO: Implementar FilePicker general
          break
      }
    } catch (error) {
      console.error("Error al adjuntar archivo:", error)
      Alert.alert("Error", "No se pudo adjuntar el archivo")
    }
  }

  const handleSubmit = async () => {
    if (!formData.title || !formData.description || !formData.service || !formData.address) {
      Alert.alert("Error", "Por favor completa todos los campos obligatorios")
      return
    }

    if (!formData.minBudget || !formData.maxBudget) {
      Alert.alert("Error", "Por favor especifica el presupuesto")
      return
    }

    try {
      await createTask({
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: {
          address: formData.address,
          coordinates: {
            latitude: -34.6037 + (Math.random() - 0.5) * 0.1,
            longitude: -58.3816 + (Math.random() - 0.5) * 0.1,
          },
        },
        budget: {
          min: Number.parseFloat(formData.minBudget),
          max: Number.parseFloat(formData.maxBudget),
        },
        urgency: formData.urgency,
        estimatedDuration: Number.parseFloat(formData.estimatedDuration) || 2,
        requirements: formData.requirements
          .split(",")
          .map((req) => req.trim())
          .filter(Boolean),
        clientId: "current_user",
      })

      Alert.alert("¡Éxito!", "Tu tarea ha sido publicada exitosamente", [
        { text: "OK", onPress: () => navigation.goBack() },
      ])
    } catch (error) {
      Alert.alert("Error", "No se pudo crear la tarea")
    }
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <TouchableWithoutFeedback onPress={() => {
        setShowDropdown(false)
        setShowAttachmentMenu(false)
      }}>
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          nestedScrollEnabled={true}
        >
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Información Básica</Text>

          {/* 1. Título de la tarea */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Título del servicio *</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Reparar grifo de cocina"
              placeholderTextColor={colors.textSecondary}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />
          </View>

          {/* Espaciado */}
          <View style={styles.spacer} />

          {/* 2. ¿En qué necesitas ayuda? */}
          <View style={[styles.inputGroup, { zIndex: 1000 }]}>
            <Text style={[styles.label, { color: colors.text }]}>¿En qué necesitas ayuda? *</Text>
            <View style={styles.dropdownContainer}>
              <TouchableOpacity
                style={[
                  styles.serviceSelector,
                  {
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    borderBottomLeftRadius: showDropdown ? 0 : 10,
                    borderBottomRightRadius: showDropdown ? 0 : 10,
                  }
                ]}
                onPress={() => setShowDropdown(!showDropdown)}
              >
                <Text style={[
                  styles.serviceSelectorText,
                  { color: formData.service ? colors.text : colors.textSecondary }
                ]}>
                  {formData.service || "Ej: Limpiar mi casa, Reparar una tubería..."}
                </Text>
                <Text style={[
                  styles.dropdownIcon,
                  {
                    color: colors.textSecondary,
                    transform: [{ rotate: showDropdown ? '180deg' : '0deg' }]
                  }
                ]}>▼</Text>
              </TouchableOpacity>


            </View>
          </View>

          {/* Dropdown que aparece después del input */}
          {showDropdown && (
            <View style={[
              styles.simpleDropdown,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
              }
            ]}>
              {/* Buscador */}
              <TextInput
                style={[
                  styles.simpleSearch,
                  {
                    backgroundColor: colors.surface,
                    color: colors.text,
                    borderColor: colors.border
                  }
                ]}
                placeholder="Buscar servicios..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />

              {/* Lista de servicios simple */}
              <ScrollView style={styles.simpleServicesList} nestedScrollEnabled>
                {filteredServices.slice(0, 8).map((service) => (
                  <TouchableOpacity
                    key={service.id}
                    style={[styles.simpleServiceItem, { borderBottomColor: colors.border }]}
                    onPress={() => {
                      setFormData({ ...formData, service: service.name, category: service.category })
                      setShowDropdown(false)
                      setSearchQuery("")
                    }}
                  >
                    <Text style={styles.simpleServiceIcon}>{service.icon}</Text>
                    <Text style={[styles.simpleServiceName, { color: colors.text }]}>
                      {service.name}
                    </Text>
                  </TouchableOpacity>
                ))}

                {/* Opción para agregar servicio personalizado cuando no hay resultados */}
                {searchQuery.length > 2 && filteredServices.length === 0 && (
                  <TouchableOpacity
                    style={[styles.addCustomServiceItem, { borderBottomColor: colors.border }]}
                    onPress={() => {
                      const customService = searchQuery.trim()
                      setFormData({ ...formData, service: customService, category: "personalizado" })
                      setShowDropdown(false)
                      setSearchQuery("")
                      saveCustomService(customService)
                    }}
                  >
                    <Text style={styles.addCustomServiceIcon}>➕</Text>
                    <Text style={[styles.addCustomServiceText, { color: colors.primary }]}>
                      Agregar "{searchQuery}" como nuevo servicio
                    </Text>
                  </TouchableOpacity>
                )}

                {/* Opción para agregar servicio personalizado cuando hay resultados */}
                {searchQuery.length > 2 && filteredServices.length > 0 && (
                  <TouchableOpacity
                    style={[styles.addCustomServiceItem, { borderBottomColor: colors.border }]}
                    onPress={() => {
                      const customService = searchQuery.trim()
                      setFormData({ ...formData, service: customService, category: "personalizado" })
                      setShowDropdown(false)
                      setSearchQuery("")
                      saveCustomService(customService)
                    }}
                  >
                    <Text style={styles.addCustomServiceIcon}>✨</Text>
                    <Text style={[styles.addCustomServiceText, { color: colors.primary }]}>
                      ¿No encuentras lo que buscas? Agregar "{searchQuery}"
                    </Text>
                  </TouchableOpacity>
                )}
              </ScrollView>
            </View>
          )}

          {/* Espaciado */}
          <View style={styles.spacer} />

          {/* 3. Descripción detallada */}
          <View style={styles.inputGroup}>
            <View style={styles.labelWithAI}>
              <Text style={[styles.label, { color: colors.text }]}>Descripción detallada *</Text>
              <TouchableOpacity
                style={[styles.aiAssistantButton, { borderColor: colors.primary }]}
                onPress={() => {
                  Alert.alert("Asistente IA", "Próximamente: Asistente IA para generar descripciones automáticamente")
                }}
              >
                <Text style={[styles.aiAssistantText, { color: colors.primary }]}>IA</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.textAreaContainer}>
              <TextInput
                style={[
                  styles.textArea,
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="Describe exactamente qué necesitas que se haga..."
                placeholderTextColor={colors.textSecondary}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                multiline
                numberOfLines={4}
              />
              {/* Botones flotantes dentro del campo */}
              <View style={styles.floatingButtonsContainer}>
                {/* Botón para adjuntar archivos con menú desplegable */}
                <View style={styles.attachmentContainer}>
                  <TouchableOpacity
                    style={[styles.attachmentButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
                    onPress={() => setShowAttachmentMenu(!showAttachmentMenu)}
                  >
                    <Text style={styles.attachmentText}>📎</Text>
                  </TouchableOpacity>

                  {/* Menú desplegable pequeño */}
                  {showAttachmentMenu && (
                    <View style={[styles.attachmentMenu, { backgroundColor: colors.background, borderColor: colors.border }]}>
                      <TouchableOpacity
                        style={styles.attachmentMenuItem}
                        onPress={() => {
                          handleAttachment('photo')
                          setShowAttachmentMenu(false)
                        }}
                      >
                        <Text style={styles.attachmentMenuIcon}>📷</Text>
                        <Text style={[styles.attachmentMenuText, { color: colors.text }]}>Foto</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.attachmentMenuItem}
                        onPress={() => {
                          handleAttachment('document')
                          setShowAttachmentMenu(false)
                        }}
                      >
                        <Text style={styles.attachmentMenuIcon}>📄</Text>
                        <Text style={[styles.attachmentMenuText, { color: colors.text }]}>Documento</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.attachmentMenuItem}
                        onPress={() => {
                          handleAttachment('file')
                          setShowAttachmentMenu(false)
                        }}
                      >
                        <Text style={styles.attachmentMenuIcon}>📁</Text>
                        <Text style={[styles.attachmentMenuText, { color: colors.text }]}>Archivo</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>


              </View>
            </View>
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Ubicación</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Dirección *</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Av. Corrientes 1234, Buenos Aires"
              placeholderTextColor={colors.textSecondary}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
            />
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Presupuesto y Tiempo</Text>

          <View style={styles.budgetRow}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={[styles.label, { color: colors.text }]}>Presupuesto mínimo *</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="50"
                placeholderTextColor={colors.textSecondary}
                value={formData.minBudget}
                onChangeText={(text) => setFormData({ ...formData, minBudget: text })}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
              <Text style={[styles.label, { color: colors.text }]}>Presupuesto máximo *</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="100"
                placeholderTextColor={colors.textSecondary}
                value={formData.maxBudget}
                onChangeText={(text) => setFormData({ ...formData, maxBudget: text })}
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Duración estimada (horas)</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="2"
              placeholderTextColor={colors.textSecondary}
              value={formData.estimatedDuration}
              onChangeText={(text) => setFormData({ ...formData, estimatedDuration: text })}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Urgencia</Text>
            <View style={styles.urgencyButtons}>
              {[
                { key: "low", label: "Flexible", color: "#34C759" },
                { key: "medium", label: "Moderado", color: "#FF9500" },
                { key: "high", label: "Urgente", color: "#FF3B30" },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.urgencyButton,
                    {
                      backgroundColor: formData.urgency === option.key ? option.color : colors.background,
                      borderColor: option.color,
                    },
                  ]}
                  onPress={() => setFormData({ ...formData, urgency: option.key as any })}
                >
                  <Text
                    style={[
                      styles.urgencyButtonText,
                      { color: formData.urgency === option.key ? "#FFFFFF" : option.color },
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Requisitos Adicionales</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Requisitos (separados por comas)</Text>
            <TextInput
              style={[
                styles.textArea,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Herramientas propias, Experiencia mínima 2 años, Referencias"
              placeholderTextColor={colors.textSecondary}
              value={formData.requirements}
              onChangeText={(text) => setFormData({ ...formData, requirements: text })}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        <TouchableOpacity style={[styles.submitButton, { backgroundColor: colors.primary }]} onPress={handleSubmit}>
          <Text style={styles.submitButtonText}>Publicar Solicitud</Text>
        </TouchableOpacity>
        </ScrollView>
      </TouchableWithoutFeedback>


    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  section: {
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    overflow: "visible",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  inputGroup: {
    marginBottom: 15,
  },
  spacer: {
    height: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  categoryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 5,
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 10,
    marginBottom: 10,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  budgetRow: {
    flexDirection: "row",
  },
  urgencyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  urgencyButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: "center",
    marginHorizontal: 5,
  },
  urgencyButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  submitButton: {
    padding: 18,
    borderRadius: 15,
    alignItems: "center",
    marginTop: 10,
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  // Nuevos estilos para el dropdown compacto
  dropdownContainer: {
    position: "relative",
    zIndex: 1000,
  },
  serviceSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
  },
  serviceSelectorText: {
    fontSize: 16,
    flex: 1,
  },
  dropdownIcon: {
    fontSize: 12,
    marginLeft: 10,
  },
  // Estilos para el campo de descripción con botones flotantes
  textAreaContainer: {
    position: 'relative',
  },
  floatingButtonsContainer: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
  },
  attachmentContainer: {
    position: 'relative',
  },
  attachmentButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  attachmentText: {
    fontSize: 14,
  },
  attachmentMenu: {
    position: 'absolute',
    bottom: 40,
    right: 0,
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 8,
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  attachmentMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  attachmentMenuIcon: {
    fontSize: 16,
    marginRight: 8,
    width: 20,
  },
  attachmentMenuText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Estilos para el botón de IA en el header
  labelWithAI: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  aiAssistantButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  aiAssistantText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Estilos para el dropdown simple
  simpleDropdown: {
    borderWidth: 1,
    borderRadius: 10,
    maxHeight: 300,
    marginTop: 5,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  simpleSearch: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    margin: 10,
    fontSize: 14,
  },
  simpleServicesList: {
    maxHeight: 200,
  },
  simpleServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
  },
  simpleServiceIcon: {
    fontSize: 20,
    marginRight: 12,
    width: 25,
    textAlign: 'center',
  },
  simpleServiceName: {
    fontSize: 15,
    fontWeight: '500',
  },

  // Estilos para servicios personalizados
  addCustomServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    backgroundColor: 'rgba(59, 130, 246, 0.05)', // Fondo azul muy suave
  },
  addCustomServiceIcon: {
    fontSize: 18,
    marginRight: 12,
    width: 25,
    textAlign: 'center',
  },
  addCustomServiceText: {
    fontSize: 14,
    fontWeight: '600',
    fontStyle: 'italic',
  },
})
