import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  Alert,
} from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { useTheme } from "../contexts/ThemeContext"
import { useTasks } from "../contexts/TaskContext"

interface TaskFlowData {
  service: any
  location: {
    address: string
    coordinates?: { latitude: number; longitude: number }
  }
  details: { [key: string]: any }
  taskSize: "small" | "medium" | "large"
  description: string
}

export default function TaskFlowScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { createTask } = useTasks()
  const { selectedService, step: initialStep = "location" } = route.params

  const [currentStep, setCurrentStep] = useState(initialStep)
  const [taskData, setTaskData] = useState<TaskFlowData>({
    service: selectedService,
    location: { address: "" },
    details: {},
    taskSize: "medium",
    description: "",
  })

  const steps = ["location", "details", "options", "description"]
  const currentStepIndex = steps.indexOf(currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex])
    } else {
      handleSubmit()
    }
  }

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1])
    } else {
      navigation.goBack()
    }
  }

  const handleSubmit = async () => {
    try {
      await createTask({
        title: selectedService.name,
        description: taskData.description,
        category: selectedService.category,
        location: {
          address: taskData.location.address,
          coordinates: taskData.location.coordinates || {
            latitude: 4.6097 + (Math.random() - 0.5) * 0.1,
            longitude: -74.0817 + (Math.random() - 0.5) * 0.1,
          },
        },
        budget: {
          min: selectedService.priceRange.min,
          max: selectedService.priceRange.max,
        },
        urgency: taskData.taskSize === "small" ? "low" : taskData.taskSize === "large" ? "high" : "medium",
        estimatedDuration: taskData.taskSize === "small" ? 1 : taskData.taskSize === "large" ? 4 : 2,
        requirements: selectedService.requirements || [],
        clientId: "current_user",
      })

      navigation.navigate("TaskResults", { 
        taskData,
        service: selectedService 
      })
    } catch (error) {
      Alert.alert("Error", "No se pudo crear la tarea")
    }
  }

  const canContinue = () => {
    switch (currentStep) {
      case "location":
        return taskData.location.address.length > 0
      case "details":
        return true // Details are optional
      case "options":
        return true // Task size is pre-selected
      case "description":
        return taskData.description.length > 0
      default:
        return false
    }
  }

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
        <View 
          style={[
            styles.progressFill, 
            { backgroundColor: colors.primary, width: `${progress}%` }
          ]} 
        />
      </View>
      <Text style={[styles.progressText, { color: colors.textSecondary }]}>
        Paso {currentStepIndex + 1} de {steps.length}
      </Text>
    </View>
  )

  const renderCompletedSection = (title: string, content: string, onEdit: () => void) => (
    <View style={[styles.completedSection, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={styles.completedHeader}>
        <Text style={[styles.completedTitle, { color: colors.text }]}>{title}</Text>
        <TouchableOpacity onPress={onEdit}>
          <Ionicons name="pencil" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      <Text style={[styles.completedContent, { color: colors.textSecondary }]}>{content}</Text>
    </View>
  )

  const renderLocationStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Ubicación de la tarea
      </Text>
      <Text style={[styles.stepSubtitle, { color: colors.textSecondary }]}>
        ¿Dónde necesitas el servicio?
      </Text>
      
      <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Ionicons name="location-outline" size={20} color={colors.textSecondary} />
        <TextInput
          style={[styles.textInput, { color: colors.text }]}
          placeholder="Ingresa tu dirección"
          placeholderTextColor={colors.textSecondary}
          value={taskData.location.address}
          onChangeText={(text) => setTaskData({
            ...taskData,
            location: { ...taskData.location, address: text }
          })}
          multiline
        />
      </View>
    </View>
  )

  const renderDetailsStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Detalles del servicio
      </Text>
      <Text style={[styles.stepSubtitle, { color: colors.textSecondary }]}>
        Cuéntanos más sobre lo que necesitas
      </Text>
      
      {/* Service-specific questions would go here */}
      <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>
          {selectedService.name}
        </Text>
        <Text style={[styles.detailsDescription, { color: colors.textSecondary }]}>
          {selectedService.description}
        </Text>
      </View>
    </View>
  )

  const renderOptionsStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Opciones de tarea
      </Text>
      <Text style={[styles.stepSubtitle, { color: colors.textSecondary }]}>
        ¿Qué tan grande es tu tarea?
      </Text>
      
      <View style={styles.optionsContainer}>
        {[
          { id: "small", label: "Pequeña", subtitle: "Est. 1 hr", icon: "⚡" },
          { id: "medium", label: "Mediana", subtitle: "Est. 2-3 hrs", icon: "⭐" },
          { id: "large", label: "Grande", subtitle: "Est. 4+ hrs", icon: "🔥" },
        ].map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionCard,
              { 
                backgroundColor: colors.surface, 
                borderColor: taskData.taskSize === option.id ? colors.primary : colors.border,
                borderWidth: taskData.taskSize === option.id ? 2 : 1,
              }
            ]}
            onPress={() => setTaskData({ ...taskData, taskSize: option.id as any })}
          >
            <View style={styles.optionHeader}>
              <Text style={styles.optionIcon}>{option.icon}</Text>
              <View style={styles.optionInfo}>
                <Text style={[styles.optionLabel, { color: colors.text }]}>
                  {option.label}
                </Text>
                <Text style={[styles.optionSubtitle, { color: colors.textSecondary }]}>
                  {option.subtitle}
                </Text>
              </View>
              {taskData.taskSize === option.id && (
                <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )

  const renderDescriptionStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Describe tu tarea
      </Text>
      <Text style={[styles.stepSubtitle, { color: colors.textSecondary }]}>
        Proporciona detalles para que los profesionales entiendan exactamente lo que necesitas
      </Text>
      
      <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <TextInput
          style={[styles.textArea, { color: colors.text }]}
          placeholder="Proporciona un resumen de lo que necesitas hacer para tu profesional. Asegúrate de incluir detalles como el tamaño de tu espacio, cualquier equipo/herramienta necesaria y cómo acceder."
          placeholderTextColor={colors.textSecondary}
          value={taskData.description}
          onChangeText={(text) => setTaskData({ ...taskData, description: text })}
          multiline
          numberOfLines={6}
          textAlignVertical="top"
        />
      </View>
    </View>
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {selectedService.name}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {renderProgressBar()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Completed sections */}
        {currentStepIndex > 0 && taskData.location.address && (
          renderCompletedSection(
            "Ubicación de la tarea",
            taskData.location.address,
            () => setCurrentStep("location")
          )
        )}
        
        {currentStepIndex > 1 && (
          renderCompletedSection(
            "Detalles",
            selectedService.name,
            () => setCurrentStep("details")
          )
        )}
        
        {currentStepIndex > 2 && (
          renderCompletedSection(
            "Opciones de tarea",
            `${taskData.taskSize === "small" ? "Pequeña" : taskData.taskSize === "large" ? "Grande" : "Mediana"} - Est. ${taskData.taskSize === "small" ? "1 hr" : taskData.taskSize === "large" ? "4+ hrs" : "2-3 hrs"}`,
            () => setCurrentStep("options")
          )
        )}

        {/* Current step */}
        {currentStep === "location" && renderLocationStep()}
        {currentStep === "details" && renderDetailsStep()}
        {currentStep === "options" && renderOptionsStep()}
        {currentStep === "description" && renderDescriptionStep()}
      </ScrollView>

      {/* Continue Button */}
      <View style={[styles.footer, { backgroundColor: colors.background, borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            { 
              backgroundColor: canContinue() ? colors.primary : colors.border,
              opacity: canContinue() ? 1 : 0.5,
            }
          ]}
          onPress={handleNext}
          disabled={!canContinue()}
        >
          <Text style={[styles.continueButtonText, { color: canContinue() ? "#FFFFFF" : colors.textSecondary }]}>
            {currentStepIndex === steps.length - 1 ? "Ver Profesionales" : "Continuar"}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: "100%",
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    textAlign: "center",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  completedSection: {
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 15,
  },
  completedHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 5,
  },
  completedTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  completedContent: {
    fontSize: 14,
  },
  stepContainer: {
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    marginBottom: 25,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    gap: 10,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    minHeight: 20,
  },
  textArea: {
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: "top",
  },
  detailsCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  detailsDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  optionsContainer: {
    gap: 12,
  },
  optionCard: {
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
  },
  optionHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  optionIcon: {
    fontSize: 20,
  },
  optionInfo: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 14,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
  },
  continueButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
})
