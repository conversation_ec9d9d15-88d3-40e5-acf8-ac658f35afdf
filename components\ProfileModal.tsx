import React from 'react'
import { 
  View, 
  Text, 
  StyleSheet, 
  Modal, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions 
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'
import { useUser } from '../contexts/UserContext'
import ProfileImagePicker from './ProfileImagePicker'

const { height } = Dimensions.get('window')

interface ProfileModalProps {
  visible: boolean
  onClose: () => void
}

export default function ProfileModal({ visible, onClose }: ProfileModalProps) {
  const { colors } = useTheme()
  const { user, updateUser } = useUser()

  const handleImageSelected = (imageUri: string) => {
    updateUser({ avatar: imageUri })
  }

  const profileDetails = [
    {
      icon: 'mail-outline',
      label: 'Email',
      value: user?.email || '<EMAIL>'
    },
    {
      icon: 'call-outline',
      label: 'Teléfono',
      value: user?.phone || '+57 ************'
    },
    {
      icon: 'location-outline',
      label: 'Ubicación',
      value: user?.fullAddress || 'Bogotá, Colombia'
    },
    {
      icon: 'calendar-outline',
      label: 'Miembro desde',
      value: 'Enero 2024'
    },
    {
      icon: 'star-outline',
      label: 'Nivel',
      value: 'Profesional Verificado'
    },
    {
      icon: 'shield-checkmark-outline',
      label: 'Verificación',
      value: user?.isVerified ? 'Cuenta Verificada' : 'Pendiente'
    }
  ]

  const stats = [
    {
      number: user?.currentRole === 'client' ? '12' : '156',
      label: user?.currentRole === 'client' ? 'Tareas creadas' : 'Trabajos completados',
      icon: 'clipboard-outline',
      color: colors.primary
    },
    {
      number: user?.currentRole === 'client' ? '4.8' : '4.9',
      label: 'Rating promedio',
      icon: 'star',
      color: '#FFD700'
    },
    {
      number: user?.currentRole === 'client' ? '2 min' : '30 min',
      label: 'Tiempo respuesta',
      icon: 'time-outline',
      color: colors.success
    },
    {
      number: '98%',
      label: 'Tasa de éxito',
      icon: 'checkmark-circle-outline',
      color: colors.success
    }
  ]

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Mi Perfil</Text>
          <TouchableOpacity style={styles.editButton}>
            <Ionicons name="create-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <View style={[styles.profileHeader, { backgroundColor: colors.surface }]}>
            <View style={styles.avatarSection}>
              <ProfileImagePicker
                currentImage={user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'}
                onImageSelected={handleImageSelected}
                size={100}
              />
              {user?.isVerified && (
                <View style={[styles.verifiedBadge, { backgroundColor: colors.primary }]}>
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                </View>
              )}
            </View>
            
            <View style={styles.profileInfo}>
              <Text style={[styles.userName, { color: colors.text }]}>
                {user?.name || 'hhh'}
              </Text>
              <Text style={[styles.userRole, { color: colors.textSecondary }]}>
                {user?.currentRole === 'client' ? 'Cliente' : 'Profesional'}
              </Text>
              <Text style={[styles.userLocation, { color: colors.textLight }]}>
                {user?.location || 'hddf'}
              </Text>
            </View>
          </View>

          {/* Statistics */}
          <View style={[styles.statsSection, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Estadísticas</Text>
            <View style={styles.statsGrid}>
              {stats.map((stat, index) => (
                <View key={index} style={[styles.statCard, { backgroundColor: colors.backgroundGray }]}>
                  <View style={[styles.statIcon, { backgroundColor: stat.color + '20' }]}>
                    <Ionicons name={stat.icon as any} size={24} color={stat.color} />
                  </View>
                  <Text style={[styles.statNumber, { color: colors.text }]}>{stat.number}</Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>{stat.label}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Profile Details */}
          <View style={[styles.detailsSection, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Información Personal</Text>
            {profileDetails.map((detail, index) => (
              <View key={index} style={[styles.detailRow, { borderBottomColor: colors.border }]}>
                <View style={[styles.detailIcon, { backgroundColor: colors.primaryLight }]}>
                  <Ionicons name={detail.icon as any} size={20} color={colors.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>{detail.label}</Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>{detail.value}</Text>
                </View>
              </View>
            ))}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionsSection}>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.primary }]}>
              <Ionicons name="create-outline" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Editar Perfil</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.surface, borderColor: colors.border, borderWidth: 1 }]}>
              <Ionicons name="share-outline" size={20} color={colors.primary} />
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>Compartir Perfil</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  editButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    alignItems: 'center',
    padding: 30,
    margin: 20,
    borderRadius: 16,
  },
  avatarSection: {
    position: 'relative',
    marginBottom: 16,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  profileInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  userLocation: {
    fontSize: 14,
    fontWeight: '400',
  },
  statsSection: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  detailsSection: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '400',
  },
  actionsSection: {
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
})
