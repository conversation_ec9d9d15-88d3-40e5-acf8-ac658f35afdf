import React from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'

interface SafetyItem {
  id: string
  title: string
  subtitle: string
  icon: string
  color: string
  action: () => void
}

export default function TrustSafetyScreen({ navigation }: any) {
  const { colors } = useTheme()

  const safetyItems: SafetyItem[] = [
    {
      id: 'identity-verification',
      title: 'Verificación de identidad',
      subtitle: 'Confirma tu identidad para mayor seguridad',
      icon: 'shield-checkmark-outline',
      color: '#10B981',
      action: () => console.log('Identity verification')
    },
    {
      id: 'rating-system',
      title: 'Sistema de calificaciones',
      subtitle: 'Califica y revisa trabajadores y clientes',
      icon: 'star-outline',
      color: '#F59E0B',
      action: () => console.log('Rating system')
    },
    {
      id: 'secure-payments',
      title: 'Pagos seguros',
      subtitle: 'Transacciones protegidas y cifradas',
      icon: 'card-outline',
      color: '#3B82F6',
      action: () => console.log('Secure payments')
    },
    {
      id: 'support-247',
      title: 'Soporte 24/7',
      subtitle: 'Ayuda disponible cuando la necesites',
      icon: 'call-outline',
      color: '#8B5CF6',
      action: () => console.log('24/7 support')
    }
  ]

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Confianza y Seguridad</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.introSection}>
          <View style={[styles.shieldIcon, { backgroundColor: colors.primaryLight }]}>
            <Ionicons name="shield-checkmark" size={32} color={colors.primary} />
          </View>
          <Text style={[styles.introTitle, { color: colors.text }]}>
            Tu seguridad es nuestra prioridad
          </Text>
          <Text style={[styles.introSubtitle, { color: colors.textSecondary }]}>
            Trabajamos constantemente para mantener una plataforma segura y confiable para todos nuestros usuarios.
          </Text>
        </View>

        <View style={styles.itemsContainer}>
          {safetyItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={[styles.safetyCard, { backgroundColor: colors.surface }]}
              onPress={item.action}
            >
              <View style={styles.cardLeft}>
                <View style={[styles.itemIcon, { backgroundColor: `${item.color}15` }]}>
                  <Ionicons name={item.icon as any} size={24} color={item.color} />
                </View>
                <View style={styles.itemText}>
                  <Text style={[styles.itemTitle, { color: colors.text }]}>
                    {item.title}
                  </Text>
                  <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
                    {item.subtitle}
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>

        <View style={[styles.infoSection, { backgroundColor: colors.primaryLight }]}>
          <Text style={[styles.infoTitle, { color: colors.primary }]}>
            ¿Cómo mantenemos la seguridad?
          </Text>
          <View style={styles.infoList}>
            <View style={styles.infoItem}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Verificación de antecedentes para todos los trabajadores
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Sistema de calificaciones y reseñas transparente
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Pagos seguros con protección contra fraudes
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Soporte 24/7 para resolver cualquier problema
              </Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.reportButton, { backgroundColor: colors.error }]}
          onPress={() => navigation.navigate('Help')}
        >
          <Ionicons name="warning-outline" size={20} color="#FFFFFF" />
          <Text style={styles.reportButtonText}>Reportar un problema</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  introSection: {
    alignItems: 'center',
    padding: 32,
  },
  shieldIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  introTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
  },
  introSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  itemsContainer: {
    paddingHorizontal: 20,
  },
  safetyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    marginBottom: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  itemText: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemSubtitle: {
    fontSize: 14,
    lineHeight: 18,
  },
  infoSection: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  infoList: {
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoText: {
    fontSize: 16,
    lineHeight: 22,
    marginLeft: 12,
    flex: 1,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  reportButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
})
