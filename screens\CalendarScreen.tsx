import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Modal,
  TextInput
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

interface CalendarEvent {
  id: string
  title: string
  description: string
  date: Date
  time: string
  type: "work" | "personal" | "goal"
  completed: boolean
}

export default function CalendarScreen() {
  const { colors } = useTheme()
  const { user } = useUser()
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [showAddModal, setShowAddModal] = useState(false)
  const [newEvent, setNewEvent] = useState({
    title: "",
    description: "",
    time: "",
    type: "work" as "work" | "personal" | "goal"
  })

  // Generar días del mes actual
  const generateCalendarDays = () => {
    const year = selectedDate.getFullYear()
    const month = selectedDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Días vacíos al inicio
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Días del mes
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date) => {
    return events.filter(event => 
      event.date.toDateString() === date.toDateString()
    )
  }

  const addEvent = () => {
    if (!newEvent.title.trim()) {
      Alert.alert("Error", "Por favor ingresa un título")
      return
    }

    const event: CalendarEvent = {
      id: Date.now().toString(),
      title: newEvent.title,
      description: newEvent.description,
      date: selectedDate,
      time: newEvent.time,
      type: newEvent.type,
      completed: false
    }

    setEvents([...events, event])
    setNewEvent({ title: "", description: "", time: "", type: "work" })
    setShowAddModal(false)
    Alert.alert("¡Éxito!", "Evento agregado al calendario")
  }

  const toggleEventComplete = (eventId: string) => {
    setEvents(events.map(event => 
      event.id === eventId ? { ...event, completed: !event.completed } : event
    ))
  }

  const deleteEvent = (eventId: string) => {
    Alert.alert(
      "Eliminar Evento",
      "¿Estás seguro de que quieres eliminar este evento?",
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Eliminar", style: "destructive", onPress: () => {
          setEvents(events.filter(event => event.id !== eventId))
        }}
      ]
    )
  }

  const monthNames = [
    "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
    "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
  ]

  const dayNames = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"]

  const changeMonth = (direction: number) => {
    const newDate = new Date(selectedDate)
    newDate.setMonth(newDate.getMonth() + direction)
    setSelectedDate(newDate)
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString()
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "work": return colors.primary
      case "personal": return colors.success
      case "goal": return colors.warning
      default: return colors.textSecondary
    }
  }

  const getEventTypeText = (type: string) => {
    switch (type) {
      case "work": return "Trabajo"
      case "personal": return "Personal"
      case "goal": return "Meta"
      default: return "Otro"
    }
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: "#F5F5F5", // Background gris como en la referencia
    },
    header: {
      backgroundColor: colors.surface,
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "bold",
      color: colors.text,
      textAlign: "center",
    },
    monthNavigation: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 16,
    },
    navButton: {
      padding: 12,
      borderRadius: 8,
      backgroundColor: colors.primary,
    },
    navButtonText: {
      color: "white",
      fontSize: 18,
      fontWeight: "bold",
    },
    monthText: {
      fontSize: 20,
      fontWeight: "600",
      color: colors.text,
    },
    calendarContainer: {
      backgroundColor: colors.surface,
      margin: 16,
      borderRadius: 12,
      padding: 16,
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    dayHeaders: {
      flexDirection: "row",
      marginBottom: 8,
    },
    dayHeader: {
      flex: 1,
      alignItems: "center",
      paddingVertical: 8,
    },
    dayHeaderText: {
      fontSize: 14,
      fontWeight: "600",
      color: colors.textSecondary,
    },
    calendarGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    dayCell: {
      width: "14.28%",
      aspectRatio: 1,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 8,
      margin: 1,
    },
    dayText: {
      fontSize: 16,
      fontWeight: "500",
    },
    todayCell: {
      backgroundColor: colors.primary + "20",
    },
    selectedCell: {
      backgroundColor: colors.primary,
    },
    selectedDayText: {
      color: "white",
      fontWeight: "bold",
    },
    eventDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      marginTop: 2,
    },
    eventsSection: {
      flex: 1,
      margin: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 12,
    },
    eventCard: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderLeftWidth: 4,
      elevation: 1,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    eventHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 8,
    },
    eventTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: colors.text,
      flex: 1,
    },
    eventTime: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 8,
    },
    eventDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    eventFooter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    eventType: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    eventTypeText: {
      fontSize: 12,
      fontWeight: "600",
      color: "white",
    },
    eventActions: {
      flexDirection: "row",
    },
    actionButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
      marginLeft: 8,
    },
    completeButton: {
      backgroundColor: colors.success,
    },
    deleteButton: {
      backgroundColor: colors.error,
    },
    actionButtonText: {
      color: "white",
      fontSize: 12,
      fontWeight: "600",
    },
    completedEvent: {
      opacity: 0.6,
    },
    addButton: {
      position: "absolute",
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: colors.primary,
      justifyContent: "center",
      alignItems: "center",
      elevation: 4,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    addButtonText: {
      color: "white",
      fontSize: 24,
      fontWeight: "bold",
    },
    emptyState: {
      alignItems: "center",
      padding: 40,
    },
    emptyStateText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: "center",
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      padding: 24,
      width: "90%",
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 20,
      textAlign: "center",
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: colors.text,
      marginBottom: 16,
    },
    typeSelector: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 20,
    },
    typeOption: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: "center",
      marginHorizontal: 4,
    },
    typeOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    typeOptionText: {
      fontSize: 14,
      color: colors.text,
    },
    typeOptionTextSelected: {
      color: "white",
    },
    modalButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    modalButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      alignItems: "center",
      marginHorizontal: 8,
    },
    cancelButton: {
      backgroundColor: colors.textSecondary,
    },
    confirmButton: {
      backgroundColor: colors.primary,
    },
    modalButtonText: {
      fontSize: 16,
      fontWeight: "600",
      color: "white",
    },
  })

  const calendarDays = generateCalendarDays()
  const selectedDateEvents = getEventsForDate(selectedDate)

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.monthNavigation}>
          <TouchableOpacity style={styles.navButton} onPress={() => changeMonth(-1)}>
            <Text style={styles.navButtonText}>‹</Text>
          </TouchableOpacity>
          
          <Text style={styles.monthText}>
            {monthNames[selectedDate.getMonth()]} {selectedDate.getFullYear()}
          </Text>
          
          <TouchableOpacity style={styles.navButton} onPress={() => changeMonth(1)}>
            <Text style={styles.navButtonText}>›</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={{ flex: 1 }}>
        <View style={styles.calendarContainer}>
          <View style={styles.dayHeaders}>
            {dayNames.map(day => (
              <View key={day} style={styles.dayHeader}>
                <Text style={styles.dayHeaderText}>{day}</Text>
              </View>
            ))}
          </View>

          <View style={styles.calendarGrid}>
            {calendarDays.map((date, index) => {
              if (!date) {
                return <View key={index} style={styles.dayCell} />
              }

              const dayEvents = getEventsForDate(date)
              const hasEvents = dayEvents.length > 0

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dayCell,
                    isToday(date) && styles.todayCell,
                    isSelected(date) && styles.selectedCell,
                  ]}
                  onPress={() => setSelectedDate(date)}
                >
                  <Text style={[
                    styles.dayText,
                    { color: colors.text },
                    isSelected(date) && styles.selectedDayText,
                  ]}>
                    {date.getDate()}
                  </Text>
                  {hasEvents && (
                    <View style={[
                      styles.eventDot,
                      { backgroundColor: isSelected(date) ? "white" : colors.primary }
                    ]} />
                  )}
                </TouchableOpacity>
              )
            })}
          </View>
        </View>

        <View style={styles.eventsSection}>
          <Text style={styles.sectionTitle}>
            Eventos - {selectedDate.toLocaleDateString('es-ES', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </Text>

          {selectedDateEvents.length > 0 ? (
            selectedDateEvents.map(event => (
              <View 
                key={event.id} 
                style={[
                  styles.eventCard,
                  { borderLeftColor: getEventTypeColor(event.type) },
                  event.completed && styles.completedEvent
                ]}
              >
                <View style={styles.eventHeader}>
                  <Text style={styles.eventTitle}>{event.title}</Text>
                  {event.time && <Text style={styles.eventTime}>{event.time}</Text>}
                </View>

                {event.description && (
                  <Text style={styles.eventDescription}>{event.description}</Text>
                )}

                <View style={styles.eventFooter}>
                  <View style={[
                    styles.eventType,
                    { backgroundColor: getEventTypeColor(event.type) }
                  ]}>
                    <Text style={styles.eventTypeText}>
                      {getEventTypeText(event.type)}
                    </Text>
                  </View>

                  <View style={styles.eventActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, styles.completeButton]}
                      onPress={() => toggleEventComplete(event.id)}
                    >
                      <Text style={styles.actionButtonText}>
                        {event.completed ? "✓" : "○"}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.actionButton, styles.deleteButton]}
                      onPress={() => deleteEvent(event.id)}
                    >
                      <Text style={styles.actionButtonText}>🗑</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                No hay eventos para esta fecha.{'\n'}Toca el botón + para agregar uno.
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setShowAddModal(true)}
      >
        <Text style={styles.addButtonText}>+</Text>
      </TouchableOpacity>

      <Modal
        visible={showAddModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Nuevo Evento</Text>

            <TextInput
              style={styles.input}
              placeholder="Título del evento"
              placeholderTextColor={colors.textSecondary}
              value={newEvent.title}
              onChangeText={(text) => setNewEvent({ ...newEvent, title: text })}
            />

            <TextInput
              style={styles.input}
              placeholder="Descripción (opcional)"
              placeholderTextColor={colors.textSecondary}
              value={newEvent.description}
              onChangeText={(text) => setNewEvent({ ...newEvent, description: text })}
              multiline
            />

            <TextInput
              style={styles.input}
              placeholder="Hora (ej: 14:30)"
              placeholderTextColor={colors.textSecondary}
              value={newEvent.time}
              onChangeText={(text) => setNewEvent({ ...newEvent, time: text })}
            />

            <View style={styles.typeSelector}>
              {["work", "personal", "goal"].map(type => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.typeOption,
                    newEvent.type === type && styles.typeOptionSelected
                  ]}
                  onPress={() => setNewEvent({ ...newEvent, type: type as any })}
                >
                  <Text style={[
                    styles.typeOptionText,
                    newEvent.type === type && styles.typeOptionTextSelected
                  ]}>
                    {getEventTypeText(type)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancelar</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={addEvent}
              >
                <Text style={styles.modalButtonText}>Agregar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}
