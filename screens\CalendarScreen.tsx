import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  FlatList,
  Dimensions
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { Calendar, LocaleConfig } from 'react-native-calendars'
import { useTheme } from '../contexts/ThemeContext'

// Configurar localización en español
LocaleConfig.locales['es'] = {
  monthNames: [
    'Enero', 'Febrero', '<PERSON><PERSON>', 'Abril', 'Mayo', '<PERSON><PERSON>',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ],
  monthNamesShort: [
    'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
    'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
  ],
  dayNames: [
    '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'
  ],
  dayNamesShort: ['Dom', 'Lun', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'V<PERSON>', '<PERSON>á<PERSON>'],
  today: 'Hoy'
}
LocaleConfig.defaultLocale = 'es'

const { width } = Dimensions.get('window')

interface CalendarEvent {
  id: string
  title: string
  description: string
  date: string
  time: string
  type: 'task' | 'appointment' | 'reminder'
  priority: 'high' | 'medium' | 'low'
  completed: boolean
}

interface MarkedDates {
  [key: string]: {
    marked?: boolean
    dotColor?: string
    selected?: boolean
    selectedColor?: string
    selectedTextColor?: string
  }
}

export default function CalendarScreen() {
  const { colors } = useTheme()

  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [showAddModal, setShowAddModal] = useState(false)
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    time: '',
    type: 'task' as const,
    priority: 'medium' as const
  })

  // Datos de ejemplo
  useEffect(() => {
    const sampleEvents: CalendarEvent[] = [
      {
        id: '1',
        title: 'Limpieza de casa',
        description: 'Servicio de limpieza profunda',
        date: new Date().toISOString().split('T')[0],
        time: '10:00',
        type: 'task',
        priority: 'high',
        completed: false
      },
      {
        id: '2',
        title: 'Reunión con cliente',
        description: 'Discutir proyecto de jardinería',
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        time: '14:30',
        type: 'appointment',
        priority: 'high',
        completed: false
      }
    ]
    setEvents(sampleEvents)
  }, [])

  const getMarkedDates = (): MarkedDates => {
    const marked: MarkedDates = {}
    
    events.forEach(event => {
      const color = getEventColor(event.type, event.priority)
      marked[event.date] = {
        marked: true,
        dotColor: color,
        selected: event.date === selectedDate,
        selectedColor: event.date === selectedDate ? colors.primary : undefined,
        selectedTextColor: event.date === selectedDate ? '#FFFFFF' : undefined
      }
    })

    if (!marked[selectedDate]) {
      marked[selectedDate] = {
        selected: true,
        selectedColor: colors.primary,
        selectedTextColor: '#FFFFFF'
      }
    }

    return marked
  }

  const getEventColor = (type: string, priority: string) => {
    if (priority === 'high') return colors.error
    if (type === 'appointment') return colors.primary
    if (type === 'reminder') return colors.warning
    return colors.success
  }

  const getEventsForDate = (date: string) => {
    return events.filter(event => event.date === date)
  }

  const addEvent = () => {
    if (!newEvent.title.trim()) {
      Alert.alert('Error', 'Por favor ingresa un título')
      return
    }

    const event: CalendarEvent = {
      id: Date.now().toString(),
      title: newEvent.title,
      description: newEvent.description,
      date: selectedDate,
      time: newEvent.time || '09:00',
      type: newEvent.type,
      priority: newEvent.priority,
      completed: false
    }

    setEvents([...events, event])
    setNewEvent({
      title: '',
      description: '',
      time: '',
      type: 'task',
      priority: 'medium'
    })
    setShowAddModal(false)
    Alert.alert('Éxito', 'Evento agregado correctamente')
  }

  const toggleEventComplete = (eventId: string) => {
    setEvents(events.map(event => 
      event.id === eventId 
        ? { ...event, completed: !event.completed }
        : event
    ))
  }

  const deleteEvent = (eventId: string) => {
    Alert.alert(
      'Eliminar evento',
      '¿Estás seguro de que quieres eliminar este evento?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive',
          onPress: () => setEvents(events.filter(event => event.id !== eventId))
        }
      ]
    )
  }

  const renderEvent = ({ item }: { item: CalendarEvent }) => (
    <View style={[styles.eventCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={styles.eventHeader}>
        <View style={styles.eventInfo}>
          <View style={styles.eventTitleRow}>
            <View style={[
              styles.eventDot, 
              { backgroundColor: getEventColor(item.type, item.priority) }
            ]} />
            <Text style={[
              styles.eventTitle, 
              { color: colors.text },
              item.completed && styles.completedText
            ]}>
              {item.title}
            </Text>
          </View>
          <Text style={[styles.eventTime, { color: colors.textSecondary }]}>
            {item.time}
          </Text>
          {item.description && (
            <Text style={[styles.eventDescription, { color: colors.textLight }]}>
              {item.description}
            </Text>
          )}
        </View>
        <View style={styles.eventActions}>
          <TouchableOpacity
            onPress={() => toggleEventComplete(item.id)}
            style={[
              styles.actionButton,
              { backgroundColor: item.completed ? colors.success + '20' : colors.backgroundGray }
            ]}
          >
            <Ionicons 
              name={item.completed ? 'checkmark-circle' : 'ellipse-outline'} 
              size={20} 
              color={item.completed ? colors.success : colors.textLight} 
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => deleteEvent(item.id)}
            style={[styles.actionButton, { backgroundColor: colors.error + '20' }]}
          >
            <Ionicons name="trash-outline" size={18} color={colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )

  const calendarTheme = {
    backgroundColor: colors.background,
    calendarBackground: colors.surface,
    textSectionTitleColor: colors.textSecondary,
    selectedDayBackgroundColor: colors.primary,
    selectedDayTextColor: '#FFFFFF',
    todayTextColor: colors.primary,
    dayTextColor: colors.text,
    textDisabledColor: colors.textLight,
    dotColor: colors.primary,
    selectedDotColor: '#FFFFFF',
    arrowColor: colors.primary,
    monthTextColor: colors.text,
    indicatorColor: colors.primary,
    textDayFontFamily: 'System',
    textMonthFontFamily: 'System',
    textDayHeaderFontFamily: 'System',
    textDayFontWeight: '400',
    textMonthFontWeight: '600',
    textDayHeaderFontWeight: '600',
    textDayFontSize: 16,
    textMonthFontSize: 18,
    textDayHeaderFontSize: 14
  }

  const todayEvents = getEventsForDate(selectedDate)

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Calendar */}
      <View style={[styles.calendarContainer, { backgroundColor: colors.surface }]}>
        <Calendar
          current={selectedDate}
          onDayPress={(day) => setSelectedDate(day.dateString)}
          markedDates={getMarkedDates()}
          theme={calendarTheme}
          firstDay={1}
          showWeekNumbers={false}
          hideExtraDays={true}
          enableSwipeMonths={true}
          style={styles.calendar}
        />
      </View>

      {/* Events Section */}
      <View style={styles.eventsSection}>
        <View style={styles.eventsSectionHeader}>
          <Text style={[styles.eventsSectionTitle, { color: colors.text }]}>
            Eventos para {(() => {
              const date = new Date(selectedDate)
              const days = ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']
              const months = [
                'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
                'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
              ]
              const dayName = days[date.getDay()]
              const dayNumber = date.getDate()
              const monthName = months[date.getMonth()]
              return `${dayName}, ${dayNumber} de ${monthName}`
            })()}
          </Text>
          <TouchableOpacity
            onPress={() => setShowAddModal(true)}
            style={[styles.addButton, { backgroundColor: colors.primary }]}
          >
            <Ionicons name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {todayEvents.length > 0 ? (
          <FlatList
            data={todayEvents}
            renderItem={renderEvent}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.eventsList}
          />
        ) : (
          <View style={styles.noEventsContainer}>
            <Ionicons name="calendar-outline" size={64} color={colors.textLight} />
            <Text style={[styles.noEventsText, { color: colors.textSecondary }]}>
              No hay eventos para este día
            </Text>
            <TouchableOpacity
              onPress={() => setShowAddModal(true)}
              style={[styles.addEventButton, { borderColor: colors.border }]}
            >
              <Text style={[styles.addEventButtonText, { color: colors.primary }]}>
                Agregar evento
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Add Event Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Nuevo Evento</Text>
            <TouchableOpacity onPress={addEvent}>
              <Text style={[styles.saveButton, { color: colors.primary }]}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Título</Text>
              <TextInput
                style={[styles.input, { color: colors.text, borderColor: colors.border, backgroundColor: colors.surface }]}
                value={newEvent.title}
                onChangeText={(text) => setNewEvent({...newEvent, title: text})}
                placeholder="Título del evento"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Descripción</Text>
              <TextInput
                style={[styles.textArea, { color: colors.text, borderColor: colors.border, backgroundColor: colors.surface }]}
                value={newEvent.description}
                onChangeText={(text) => setNewEvent({...newEvent, description: text})}
                placeholder="Descripción del evento"
                placeholderTextColor={colors.textLight}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Hora</Text>
              <TextInput
                style={[styles.input, { color: colors.text, borderColor: colors.border, backgroundColor: colors.surface }]}
                value={newEvent.time}
                onChangeText={(text) => setNewEvent({...newEvent, time: text})}
                placeholder="09:00"
                placeholderTextColor={colors.textLight}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Tipo</Text>
              <View style={styles.typeButtons}>
                {(['task', 'appointment', 'reminder'] as const).map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => setNewEvent({...newEvent, type})}
                    style={[
                      styles.typeButton,
                      {
                        backgroundColor: newEvent.type === type ? colors.primary : colors.surface,
                        borderColor: colors.border
                      }
                    ]}
                  >
                    <Text style={[
                      styles.typeButtonText,
                      { color: newEvent.type === type ? '#FFFFFF' : colors.text }
                    ]}>
                      {type === 'task' ? 'Tarea' : type === 'appointment' ? 'Cita' : 'Recordatorio'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Prioridad</Text>
              <View style={styles.typeButtons}>
                {(['low', 'medium', 'high'] as const).map((priority) => (
                  <TouchableOpacity
                    key={priority}
                    onPress={() => setNewEvent({...newEvent, priority})}
                    style={[
                      styles.typeButton,
                      {
                        backgroundColor: newEvent.priority === priority ? colors.primary : colors.surface,
                        borderColor: colors.border
                      }
                    ]}
                  >
                    <Text style={[
                      styles.typeButtonText,
                      { color: newEvent.priority === priority ? '#FFFFFF' : colors.text }
                    ]}>
                      {priority === 'low' ? 'Baja' : priority === 'medium' ? 'Media' : 'Alta'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  calendarContainer: {
    margin: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  calendar: {
    borderRadius: 16,
  },
  eventsSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  eventsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  eventsSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textTransform: 'capitalize',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  eventsList: {
    paddingBottom: 20,
  },
  eventCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  eventInfo: {
    flex: 1,
    marginRight: 12,
  },
  eventTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  eventDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  completedText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  eventTime: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  eventDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  eventActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noEventsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  noEventsText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  addEventButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  addEventButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    height: 80,
    textAlignVertical: 'top',
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
})
