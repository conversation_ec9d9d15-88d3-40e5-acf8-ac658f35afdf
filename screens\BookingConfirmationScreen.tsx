import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'

export default function BookingConfirmationScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { professional, date, time, taskData } = route.params

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price * 1000)
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Confirmar Reserva</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Professional Info */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.professionalInfo}>
            <Image source={{ uri: professional.avatar }} style={styles.avatar} />
            <View style={styles.professionalDetails}>
              <Text style={[styles.professionalName, { color: colors.text }]}>
                {professional.name}
              </Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={[styles.rating, { color: colors.textSecondary }]}>
                  {professional.rating} ({professional.reviewCount} reseñas)
                </Text>
              </View>
              <Text style={[styles.rate, { color: '#10B981' }]}>
                {formatPrice(professional.hourlyRate)}/hr
              </Text>
            </View>
          </View>
        </View>

        {/* Task Details */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Detalles del Servicio</Text>
          <Text style={[styles.taskTitle, { color: colors.text }]}>
            {taskData.title || 'Ensamble de Muebles'}
          </Text>
          <Text style={[styles.taskDescription, { color: colors.textSecondary }]}>
            {taskData.description || 'Necesito ayuda con el ensamble de muebles'}
          </Text>
        </View>

        {/* Date & Time */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Fecha y Hora</Text>
          <View style={styles.dateTimeContainer}>
            <Ionicons name="calendar-outline" size={20} color={colors.primary} />
            <Text style={[styles.dateTime, { color: colors.text }]}>
              {date} - {time}
            </Text>
          </View>
        </View>

        {/* Location */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Ubicación</Text>
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={20} color={colors.primary} />
            <Text style={[styles.location, { color: colors.text }]}>
              {taskData.address || 'Carrera 15 #93-47, Bogotá'}
            </Text>
          </View>
        </View>

        {/* Price Breakdown */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Resumen de Costos</Text>
          <View style={styles.priceRow}>
            <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
              Tarifa por hora (estimado 2 horas)
            </Text>
            <Text style={[styles.priceValue, { color: '#10B981' }]}>
              {formatPrice(professional.hourlyRate * 2)}
            </Text>
          </View>
          <View style={styles.priceRow}>
            <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
              Tarifa de servicio
            </Text>
            <Text style={[styles.priceValue, { color: '#10B981' }]}>
              {formatPrice(5)}
            </Text>
          </View>
          <View style={[styles.priceRow, styles.totalRow, { borderTopColor: colors.border }]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}>Total</Text>
            <Text style={[styles.totalValue, { color: '#10B981' }]}>
              {formatPrice(professional.hourlyRate * 2 + 5)}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={[styles.footer, { borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.confirmButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('Chat', { professionalId: professional.id })}
        >
          <Text style={styles.confirmButtonText}>Confirmar y Contactar</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    padding: 20,
    borderRadius: 12,
    marginVertical: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
  },
  professionalInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  professionalDetails: {
    flex: 1,
  },
  professionalName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    marginBottom: 5,
  },
  rating: {
    fontSize: 14,
  },
  rate: {
    fontSize: 16,
    fontWeight: '600',
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  taskDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  dateTime: {
    fontSize: 16,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  location: {
    fontSize: 16,
    flex: 1,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    paddingTop: 15,
    marginTop: 10,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
  },
  confirmButton: {
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
})
