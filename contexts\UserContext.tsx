import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  currentRole: "client" | "worker" // Rol activo actual
  roles: ("client" | "worker")[] // Roles disponibles para el usuario
  avatar?: string
  bio?: string
  rating?: number
  completedTasks?: number
  isVerified?: boolean
  // Perfil de Cliente
  clientProfile?: {
    savedAddresses: string[]
    paymentMethods: string[]
    preferences: string[]
    taskHistory: string[]
  }
  // Perfil de Trabajador
  workerProfile?: {
    skills: string[]
    hourlyRate?: number
    availability?: string[]
    tools?: string[]
    vehicles?: string[]
    businessPhotos?: string[]
    aboutMe?: string
    quickFacts?: string[]
    serviceAreas?: string[]
  }
}

interface UserContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  register: (userData: Omit<User, "id"> & { password: string }) => Promise<boolean>
  logout: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  switchRole: (role: "client" | "worker") => void
  canSwitchToRole: (role: "client" | "worker") => boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const userData = await AsyncStorage.getItem("user")
      if (userData) {
        const parsedUser = JSON.parse(userData)

        // Migración de datos para usuarios existentes
        const migratedUser: User = {
          ...parsedUser,
          currentRole: parsedUser.currentRole || parsedUser.role || "client",
          roles: parsedUser.roles || [parsedUser.role || "client"],
          isVerified: parsedUser.isVerified || false,
          clientProfile: parsedUser.clientProfile || {
            savedAddresses: [],
            paymentMethods: [],
            preferences: [],
            taskHistory: []
          },
          workerProfile: parsedUser.workerProfile || undefined
        }

        setUser(migratedUser)
        // Guardar la versión migrada
        await AsyncStorage.setItem("user", JSON.stringify(migratedUser))
      }
    } catch (error) {
      console.error("Error loading user:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock user data
      const mockUser: User = {
        id: "1",
        email,
        name: email.split("@")[0].charAt(0).toUpperCase() + email.split("@")[0].slice(1),
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        bio: "Miembro activo de TaskApp desde 2024. Me gusta mantener mi hogar organizado y ayudar a otros con sus proyectos.",
        phone: "+57 ************",
        currentRole: "client",
        roles: ["client", "worker"], // Usuario híbrido para testing
        rating: 4.8,
        completedTasks: 12,
        isVerified: true,
        clientProfile: {
          savedAddresses: ["Calle 123 #45-67, Bogotá"],
          paymentMethods: ["Tarjeta **** 1234"],
          preferences: ["Limpieza profunda", "Cuidado mascotas"],
          taskHistory: []
        },
        workerProfile: {
          skills: ["Limpieza", "Plomería", "Electricidad"],
          hourlyRate: 25000,
          availability: ["Lunes a Viernes", "9:00 AM - 6:00 PM"],
          tools: ["Herramientas básicas", "Aspiradora"],
          vehicles: ["Bicicleta"],
          businessPhotos: [],
          aboutMe: "Trabajador confiable con 5 años de experiencia",
          quickFacts: ["Puntual", "Responsable", "Experiencia verificada"],
          serviceAreas: ["Bogotá", "Suba", "Chapinero"]
        }
      }

      setUser(mockUser)
      await AsyncStorage.setItem("user", JSON.stringify(mockUser))
      return true
    } catch (error) {
      console.error("Login error:", error)
      return false
    }
  }

  const register = async (userData: Omit<User, "id"> & { password: string }): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newUser: User = {
        ...userData,
        id: Date.now().toString(),
        currentRole: userData.currentRole || "client",
        roles: [userData.currentRole || "client"],
        rating: 5.0,
        completedTasks: 0,
        isVerified: false,
        clientProfile: userData.currentRole === "client" ? {
          savedAddresses: [],
          paymentMethods: [],
          preferences: [],
          taskHistory: []
        } : undefined,
        workerProfile: userData.currentRole === "worker" ? {
          skills: [],
          hourlyRate: 0,
          availability: [],
          tools: [],
          vehicles: [],
          businessPhotos: [],
          aboutMe: "",
          quickFacts: [],
          serviceAreas: []
        } : undefined
      }

      setUser(newUser)
      await AsyncStorage.setItem("user", JSON.stringify(newUser))
      return true
    } catch (error) {
      console.error("Register error:", error)
      return false
    }
  }

  const logout = async () => {
    try {
      await AsyncStorage.removeItem("user")
      setUser(null)
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) return

    try {
      const updatedUser = { ...user, ...updates }
      setUser(updatedUser)
      await AsyncStorage.setItem("user", JSON.stringify(updatedUser))
    } catch (error) {
      console.error("Update profile error:", error)
    }
  }

  const switchRole = (role: "client" | "worker") => {
    if (!user || !user.roles?.includes(role)) return

    const updatedUser = { ...user, currentRole: role }
    setUser(updatedUser)
    AsyncStorage.setItem("user", JSON.stringify(updatedUser))
  }

  const canSwitchToRole = (role: "client" | "worker"): boolean => {
    return user?.roles?.includes(role) || false
  }

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        login,
        register,
        logout,
        updateProfile,
        switchRole,
        canSwitchToRole,
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error("useUser debe ser usado dentro de un UserProvider")
  }
  return context
}
