import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function EditProfileScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user, updateProfile } = useUser()
  
  const [formData, setFormData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    address: user?.address || "",
    bio: user?.bio || "",
  })

  const handleSave = async () => {
    try {
      await updateProfile(formData)
      Alert.alert("Éxito", "Perfil actualizado correctamente")
      navigation.goBack()
    } catch (error) {
      Alert.alert("Error", "No se pudo actualizar el perfil")
    }
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={[styles.backButton, { color: colors.primary }]}>← Volver</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Editar Perfil</Text>
        <TouchableOpacity onPress={handleSave}>
          <Text style={[styles.saveButton, { color: colors.primary }]}>Guardar</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Información Básica</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.textSecondary }]}>Nombre completo</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surfaceGray, color: colors.text, borderColor: colors.border }]}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder="Tu nombre completo"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.textSecondary }]}>Email</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surfaceGray, color: colors.text, borderColor: colors.border }]}
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.textLight}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.textSecondary }]}>Teléfono</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surfaceGray, color: colors.text, borderColor: colors.border }]}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              placeholder="+57 ************"
              placeholderTextColor={colors.textLight}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.textSecondary }]}>Dirección</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surfaceGray, color: colors.text, borderColor: colors.border }]}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              placeholder="Tu dirección"
              placeholderTextColor={colors.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.textSecondary }]}>Biografía</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: colors.surfaceGray, color: colors.text, borderColor: colors.border }]}
              value={formData.bio}
              onChangeText={(text) => setFormData({ ...formData, bio: text })}
              placeholder="Cuéntanos sobre ti y tu experiencia..."
              placeholderTextColor={colors.textLight}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Configuración de Cuenta</Text>
          
          <TouchableOpacity style={[styles.optionButton, { borderColor: colors.border }]}>
            <Text style={[styles.optionText, { color: colors.text }]}>🔒 Cambiar Contraseña</Text>
            <Text style={[styles.optionArrow, { color: colors.textSecondary }]}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.optionButton, { borderColor: colors.border }]}>
            <Text style={[styles.optionText, { color: colors.text }]}>🔔 Configurar Notificaciones</Text>
            <Text style={[styles.optionArrow, { color: colors.textSecondary }]}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.optionButton, { borderColor: colors.border }]}>
            <Text style={[styles.optionText, { color: colors.text }]}>🌍 Idioma y Región</Text>
            <Text style={[styles.optionArrow, { color: colors.textSecondary }]}>→</Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Privacidad</Text>
          
          <TouchableOpacity style={[styles.optionButton, { borderColor: colors.border }]}>
            <Text style={[styles.optionText, { color: colors.text }]}>👁️ Configurar Privacidad</Text>
            <Text style={[styles.optionArrow, { color: colors.textSecondary }]}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.optionButton, { borderColor: colors.border }]}>
            <Text style={[styles.optionText, { color: colors.text }]}>📊 Datos y Actividad</Text>
            <Text style={[styles.optionArrow, { color: colors.textSecondary }]}>→</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  backButton: {
    fontSize: 16,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "700",
  },
  saveButton: {
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  section: {
    marginHorizontal: 24,
    marginVertical: 8,
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    fontSize: 16,
    minHeight: 100,
  },
  optionButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  optionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  optionArrow: {
    fontSize: 16,
    fontWeight: "bold",
  },
})
