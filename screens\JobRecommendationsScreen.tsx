import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useSkills, JobRecommendation } from "../contexts/SkillsContext"

export default function JobRecommendationsScreen() {
  const { colors } = useTheme()
  const { recommendations, refreshRecommendations, isLoading, skills } = useSkills()
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadRecommendations()
  }, [])

  const loadRecommendations = async () => {
    if (skills.filter(s => s.isActive).length === 0) {
      return
    }
    await refreshRecommendations()
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await refreshRecommendations()
    setRefreshing(false)
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high": return "#EF4444"
      case "medium": return "#F59E0B"
      default: return "#10B981"
    }
  }

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "high": return "Urgente"
      case "medium": return "Moderado"
      default: return "Flexible"
    }
  }

  const handleJobPress = (job: JobRecommendation) => {
    Alert.alert(
      job.title,
      `${job.description}\n\nUbicación: ${job.location}\nTarifa: $${job.hourlyRate}/hr\nDuración: ${job.estimatedDuration}\nCompatibilidad: ${job.matchScore.toFixed(0)}%`,
      [
        { text: "Cerrar", style: "cancel" },
        { text: "Aplicar", onPress: () => applyToJob(job) }
      ]
    )
  }

  const applyToJob = (job: JobRecommendation) => {
    Alert.alert("¡Aplicación Enviada!", `Has aplicado al trabajo: ${job.title}`)
  }

  const renderJobCard = (job: JobRecommendation) => (
    <TouchableOpacity
      key={job.id}
      style={styles.jobCard}
      onPress={() => handleJobPress(job)}
    >
      <View style={styles.jobHeader}>
        <View style={styles.jobTitleContainer}>
          <Text style={styles.jobTitle}>{job.title}</Text>
          <View style={[
            styles.urgencyBadge,
            { backgroundColor: getUrgencyColor(job.urgency) }
          ]}>
            <Text style={styles.urgencyText}>{getUrgencyText(job.urgency)}</Text>
          </View>
        </View>
        <Text style={styles.jobRate}>${job.hourlyRate}/hr</Text>
      </View>

      <Text style={styles.jobDescription}>{job.description}</Text>

      <View style={styles.jobInfo}>
        <Text style={styles.jobLocation}>📍 {job.location}</Text>
        <Text style={styles.jobDistance}>{job.distance.toFixed(1)} km</Text>
      </View>

      <View style={styles.jobDetails}>
        <Text style={styles.jobDuration}>⏱️ {job.estimatedDuration}</Text>
        <Text style={styles.jobRating}>⭐ {job.clientRating.toFixed(1)}</Text>
      </View>

      <View style={styles.skillsRequired}>
        <Text style={styles.skillsLabel}>Habilidades requeridas:</Text>
        <View style={styles.skillsContainer}>
          {job.requiredSkills.map((skill, index) => (
            <View key={index} style={styles.skillTag}>
              <Text style={styles.skillTagText}>{skill}</Text>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.matchScore}>
        <View style={styles.matchBar}>
          <View 
            style={[
              styles.matchFill,
              { 
                width: `${job.matchScore}%`,
                backgroundColor: job.matchScore >= 80 ? "#10B981" : 
                                job.matchScore >= 60 ? "#F59E0B" : "#EF4444"
              }
            ]}
          />
        </View>
        <Text style={styles.matchText}>
          {job.matchScore.toFixed(0)}% compatible
        </Text>
      </View>
    </TouchableOpacity>
  )

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 8,
    },
    headerSubtitle: {
      fontSize: 16,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
    },
    jobCard: {
      margin: 16,
      padding: 16,
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    jobHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 12,
    },
    jobTitleContainer: {
      flex: 1,
      marginRight: 12,
    },
    jobTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.text,
      marginBottom: 8,
    },
    urgencyBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: "flex-start",
    },
    urgencyText: {
      fontSize: 12,
      fontWeight: "600",
      color: "white",
    },
    jobRate: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors.primary,
    },
    jobDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 12,
      lineHeight: 20,
    },
    jobInfo: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 8,
    },
    jobLocation: {
      fontSize: 14,
      color: colors.text,
    },
    jobDistance: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    jobDetails: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 12,
    },
    jobDuration: {
      fontSize: 14,
      color: colors.text,
    },
    jobRating: {
      fontSize: 14,
      color: colors.text,
    },
    skillsRequired: {
      marginBottom: 12,
    },
    skillsLabel: {
      fontSize: 14,
      fontWeight: "600",
      color: colors.text,
      marginBottom: 8,
    },
    skillsContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
    },
    skillTag: {
      backgroundColor: colors.primary + "20",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginRight: 8,
      marginBottom: 4,
    },
    skillTagText: {
      fontSize: 12,
      color: colors.primary,
      fontWeight: "500",
    },
    matchScore: {
      flexDirection: "row",
      alignItems: "center",
    },
    matchBar: {
      flex: 1,
      height: 8,
      backgroundColor: colors.border,
      borderRadius: 4,
      marginRight: 12,
      overflow: "hidden",
    },
    matchFill: {
      height: "100%",
      borderRadius: 4,
    },
    matchText: {
      fontSize: 14,
      fontWeight: "600",
      color: colors.text,
    },
    emptyState: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 40,
    },
    emptyStateText: {
      fontSize: 18,
      color: colors.textSecondary,
      textAlign: "center",
      marginBottom: 16,
    },
    emptyStateSubtext: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: "center",
    },
    loadingText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: "center",
      padding: 20,
    },
  })

  const activeSkills = skills.filter(s => s.isActive)

  if (activeSkills.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Trabajos Recomendados</Text>
          <Text style={styles.headerSubtitle}>Basados en tus habilidades</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            No tienes habilidades activas
          </Text>
          <Text style={styles.emptyStateSubtext}>
            Agrega y activa algunas habilidades para ver recomendaciones de trabajos
          </Text>
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Trabajos Recomendados</Text>
        <Text style={styles.headerSubtitle}>
          {recommendations.length} trabajos encontrados
        </Text>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {isLoading ? (
          <Text style={styles.loadingText}>Buscando trabajos...</Text>
        ) : recommendations.length > 0 ? (
          recommendations.map(renderJobCard)
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No hay trabajos disponibles
            </Text>
            <Text style={styles.emptyStateSubtext}>
              Desliza hacia abajo para actualizar
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  )
}
