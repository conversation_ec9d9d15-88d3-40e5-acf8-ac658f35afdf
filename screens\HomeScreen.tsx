"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Dimensions, ActivityIndicator } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useTasks } from "../contexts/TaskContext"
import { useServices } from "../contexts/ServicesContext"
import RoleSwitcher from "../components/RoleSwitcher"

const { width } = Dimensions.get("window")

export default function HomeScreen({ navigation }: any) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user } = useUser()
  const { availableTasks, myTasks } = useTasks()
  const { categories, searchServices, getPopularServices } = useServices()
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Buscar servicios cuando cambia el query
  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      setIsLoading(true)
      // Simular delay de búsqueda real
      setTimeout(() => {
        const results = searchServices(query)
        setSearchResults(results)
        setIsLoading(false)
      }, 300)
    } else {
      setSearchResults([])
      setIsLoading(false)
    }
  }

  const navigateToCategory = (categoryId: string) => {
    navigation.navigate("CategoryServices", { categoryId })
  }

  const navigateToService = (serviceId: string) => {
    navigation.navigate("ServiceDetail", { serviceId })
  }

  const filteredTasks = availableTasks.filter(
    (task) =>
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.category.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "#FF3B30" // Rojo para urgente
      case "medium":
        return "#FF9500" // Naranja para moderado
      case "low":
        return "#34C759" // Verde para flexible
      default:
        return colors.textSecondary
    }
  }

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "Urgente"
      case "medium":
        return "Moderado"
      case "low":
        return "Flexible"
      default:
        return ""
    }
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header limpio */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.greeting, { color: colors.text }]}>¡Hola, {user?.name}!</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {user?.currentRole === "client" ? "¿Qué necesitas hacer hoy?" : "Encuentra tu próximo trabajo"}
          </Text>
        </View>

        {/* Search Bar más limpio */}
        <View style={[styles.searchContainer, { backgroundColor: colors.backgroundGray }]}>
          <Ionicons name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder={user?.currentRole === "client" ? "Buscar: limpieza, plomería, cuidado..." : "Buscar trabajos disponibles..."}
            placeholderTextColor={colors.textLight}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      {/* Role Switcher - Solo si tiene múltiples roles */}
      {user?.roles && user.roles.length > 1 && (
        <View style={[styles.roleSwitcherContainer, { backgroundColor: colors.surface }]}>
          <RoleSwitcher />
        </View>
      )}

      {/* Quick Actions */}
      {user?.role === "client" && (
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.createTaskButton, { backgroundColor: '#4A90E2' }]}
            onPress={() => navigation.navigate("PopularProjects")}
            activeOpacity={0.8}
          >
            <View style={styles.createTaskContent}>
              <View style={styles.createTaskIconContainer}>
                <Text style={styles.createTaskIcon}>+</Text>
              </View>
              <View style={styles.createTaskTextContainer}>
                <Text style={styles.createTaskText}>Solicitar Servicio</Text>
                <Text style={styles.createTaskSubtext}>Publica lo que necesitas y recibe propuestas</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      )}

      {/* Breadcrumbs */}
      <View style={[styles.breadcrumbContainer, { backgroundColor: colors.surface }]}>
        <Text style={[styles.breadcrumb, { color: colors.textSecondary }]}>
          Inicio {searchQuery.trim() && `> Búsqueda: "${searchQuery}"`}
        </Text>
      </View>

      {/* Search Results */}
      {searchQuery.trim() && (
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.searchResultsHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Resultados para "{searchQuery}"
            </Text>
            {isLoading && <ActivityIndicator size="small" color={colors.primary} />}
          </View>

          {!isLoading && searchResults.length > 0 && (
            <>
              {searchResults.slice(0, 5).map((service) => (
                <TouchableOpacity
                  key={service.id}
                  style={[styles.serviceCard, { backgroundColor: colors.background, borderColor: colors.border }]}
                  onPress={() => navigateToService(service.id)}
                >
                  <View style={styles.serviceHeader}>
                    <Text style={styles.serviceIcon}>{service.icon}</Text>
                    <View style={styles.serviceInfo}>
                      <Text style={[styles.serviceName, { color: colors.text }]}>{service.name}</Text>
                      <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
                        {service.description}
                      </Text>
                    </View>
                    <View style={styles.servicePricing}>
                      <Text style={[styles.servicePrice, { color: colors.text }]}>
                        ${service.priceRange.min.toLocaleString()} - ${service.priceRange.max.toLocaleString()}
                      </Text>
                      <Text style={[styles.servicePriceUnit, { color: colors.textLight }]}>
                        por {service.priceRange.unit}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
              {searchResults.length > 5 && (
                <TouchableOpacity
                  style={[styles.viewMoreButton, { borderColor: colors.primary }]}
                  onPress={() => navigation.navigate("AllCategories", { searchQuery })}
                >
                  <Text style={[styles.viewMoreText, { color: colors.primary }]}>
                    Ver todos los resultados ({searchResults.length})
                  </Text>
                </TouchableOpacity>
              )}
            </>
          )}

          {!isLoading && searchResults.length === 0 && (
            <View style={styles.noResultsContainer}>
              <Ionicons name="search-outline" size={48} color={colors.textLight} />
              <Text style={[styles.noResultsText, { color: colors.textSecondary }]}>
                No se encontraron servicios para "{searchQuery}"
              </Text>
              <Text style={[styles.noResultsSubtext, { color: colors.textLight }]}>
                Intenta con otros términos o explora nuestras categorías
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Categories */}
      {!searchQuery.trim() && (
        <View style={styles.categoriesSection}>
          <View style={styles.categoriesSectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Categorías de Servicios</Text>
            <TouchableOpacity onPress={() => navigation.navigate("AllCategories")}>
              <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver todo</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.categoriesGrid}>
            {/* Primera fila fija - 4 categorías principales */}
            <View style={styles.categoriesRow}>
              {categories.slice(0, 4).map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[styles.categoryCard, { backgroundColor: colors.surface }]}
                  onPress={() => navigateToCategory(category.id)}
                >
                  <View style={[styles.categoryIcon, { backgroundColor: '#F3F4F6' }]}>
                    <Text style={styles.categoryEmoji}>{category.icon}</Text>
                  </View>
                  <Text style={[styles.categoryName, { color: colors.text }]} numberOfLines={1}>
                    {category.name}
                  </Text>
                  <Text style={[styles.categoryCount, { color: colors.textLight }]}>
                    {category.services.length} servicios
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Segunda fila con scroll horizontal - resto de categorías */}
            {categories.length > 4 && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoriesScrollRow}
                style={styles.categoriesRowContainer}
              >
                {categories.slice(4).map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[styles.categoryCard, styles.scrollCategoryCard, { backgroundColor: colors.surface }]}
                    onPress={() => navigateToCategory(category.id)}
                  >
                    <View style={[styles.categoryIcon, { backgroundColor: '#F3F4F6' }]}>
                      <Text style={styles.categoryEmoji}>{category.icon}</Text>
                    </View>
                    <Text style={[styles.categoryName, { color: colors.text }]} numberOfLines={1}>
                      {category.name}
                    </Text>
                    <Text style={[styles.categoryCount, { color: colors.textLight }]}>
                      {category.services.length} servicios
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            )}
          </View>
        </View>
      )}

      {/* Recent/Available Tasks */}
      <View style={styles.categoriesSection}>
        <View style={styles.categoriesSectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {user?.role === "client" ? "Tareas Recientes" : "Trabajos Disponibles"}
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate("Tasks")}>
            <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver todas</Text>
          </TouchableOpacity>
        </View>

        {filteredTasks.slice(0, 3).map((task) => (
          <TouchableOpacity
            key={task.id}
            style={[styles.taskCard, { backgroundColor: colors.surface }]}
            onPress={() => navigation.navigate("TaskDetail", { taskId: task.id })}
          >
            <View style={styles.taskHeader}>
              <Text style={[styles.taskTitle, { color: colors.text }]}>{task.title}</Text>
              <View style={styles.urgencyBadge}>
                <Text style={[styles.urgencyText, { color: getUrgencyColor(task.urgency) }]}>{getUrgencyText(task.urgency)}</Text>
              </View>
            </View>

            <Text style={[styles.taskDescription, { color: colors.textSecondary }]} numberOfLines={2}>
              {task.description}
            </Text>

            <View style={styles.taskFooter}>
              <View style={styles.taskInfo}>
                <Text style={[styles.taskCategory, { color: colors.textSecondary }]}>📍 {task.category}</Text>
                <Text style={[styles.taskLocation, { color: colors.textSecondary }]}>
                  {task.location.address.split(",")[0]}
                </Text>
              </View>
              <View style={styles.taskBudget}>
                <Text style={[styles.budgetText, { color: colors.text }]}>
                  ${task.budget.min} - ${task.budget.max}
                </Text>
                <Text style={[styles.durationText, { color: colors.textSecondary }]}>~{task.estimatedDuration}h</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>



      {/* Stats */}
      {user?.role === "worker" && (
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Tu Rendimiento</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>12</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Trabajos Completados</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.warning }]}>4.9</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calificación</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.warning }]}>$1,240</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Ganado este mes</Text>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  headerContent: {
    marginBottom: 20,
  },
  roleSwitcherContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 8,
  },
  greeting: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 22,
  },
  themeToggle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  themeIcon: {
    fontSize: 20,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 4,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "400",
  },
  quickActions: {
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  createTaskButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    marginHorizontal: 4,
  },
  createTaskContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  createTaskIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  createTaskIcon: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  createTaskTextContainer: {
    flex: 1,
  },
  createTaskText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: 0.3,
  },
  createTaskSubtext: {
    color: "rgba(255, 255, 255, 0.85)",
    fontSize: 12,
    fontWeight: "400",
    marginTop: 2,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  categoriesSection: {
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 20,
    paddingHorizontal: 0,
  },
  categoriesSectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
    paddingHorizontal: 0,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
  },
  sectionSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: "600",
  },
  serviceCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  serviceHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  serviceIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  serviceInfo: {
    flex: 1,
    marginRight: 12,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  servicePricing: {
    alignItems: "flex-end",
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 2,
  },
  servicePriceUnit: {
    fontSize: 12,
  },
  categoriesGrid: {
    paddingHorizontal: 0,
  },
  categoriesRowContainer: {
    marginBottom: 15,
    height: 120, // Altura fija para evitar que se corten los iconos
  },
  categoriesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  categoriesScrollRow: {
    paddingHorizontal: 0,
    paddingRight: 20,
    paddingVertical: 5, // Padding vertical para evitar cortes
  },
  categoryCard: {
    flex: 1,
    alignItems: "center",
    padding: 12,
    borderRadius: 16,
    marginHorizontal: 4,
  },
  scrollCategoryCard: {
    flex: 0,
    width: 90,
    marginRight: 12,
  },
  seeMoreCard: {
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  categoryEmoji: {
    fontSize: 20,
  },
  categoryName: {
    fontSize: 11,
    fontWeight: "500",
    textAlign: "center",
    marginBottom: 2,
  },
  seeMoreIcon: {
    fontSize: 14,
    fontWeight: "600",
  },
  categoryCount: {
    fontSize: 10,
    textAlign: "center",
  },
  taskCard: {
    padding: 15,
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
    marginRight: 10,
  },
  urgencyBadge: {
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  urgencyText: {
    fontSize: 10,
    fontWeight: "600",
  },
  taskDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  taskFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  taskInfo: {
    flex: 1,
  },
  taskCategory: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 2,
  },
  taskLocation: {
    fontSize: 12,
  },
  taskBudget: {
    alignItems: "flex-end",
  },
  budgetText: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 2,
  },
  durationText: {
    fontSize: 12,
  },

  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  // Nuevos estilos
  breadcrumbContainer: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  breadcrumb: {
    fontSize: 12,
    fontWeight: '500',
  },
  searchResultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  viewMoreButton: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  viewMoreText: {
    fontSize: 14,
    fontWeight: '600',
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  noResultsSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
})
