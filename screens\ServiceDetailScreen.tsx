import React from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useServices } from "../contexts/ServicesContext"

export default function ServiceDetailScreen({ route, navigation }: any) {
  const { serviceId } = route.params
  const { colors } = useTheme()
  const { getAllServices } = useServices()
  
  const allServices = getAllServices()
  const service = allServices.find(s => s.id === serviceId)

  if (!service) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>Servicio no encontrado</Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Volver</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    )
  }

  const formatPrice = () => {
    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(num)
    }
    
    if (service.priceRange.unit === "servicio") {
      return `${formatNumber(service.priceRange.min)} - ${formatNumber(service.priceRange.max)}`
    }
    return `${formatNumber(service.priceRange.min)} - ${formatNumber(service.priceRange.max)} por ${service.priceRange.unit}`
  }

  const formatDuration = () => {
    if (service.duration.min === service.duration.max) {
      return `${service.duration.min} ${service.duration.unit}`
    }
    return `${service.duration.min}-${service.duration.max} ${service.duration.unit}`
  }

  const handleRequestService = () => {
    navigation.navigate("TaskFlow", { 
      selectedService: service,
      step: "details"
    })
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={[styles.backButtonText, { color: colors.primary }]}>← Volver</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Detalles del Servicio</Text>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Service Header */}
        <View style={[styles.serviceHeader, { backgroundColor: colors.surface }]}>
          <Text style={styles.serviceIcon}>{service.icon}</Text>
          <View style={styles.serviceInfo}>
            <Text style={[styles.serviceName, { color: colors.text }]}>{service.name}</Text>
            <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
              {service.description}
            </Text>
            {service.popular && (
              <View style={[styles.popularBadge, { backgroundColor: colors.warning }]}>
                <Text style={styles.popularText}>⭐ Popular</Text>
              </View>
            )}
          </View>
        </View>

        {/* Price and Duration */}
        <View style={[styles.priceSection, { backgroundColor: colors.surface }]}>
          <View style={styles.priceRow}>
            <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>Precio estimado:</Text>
            <Text style={[styles.priceValue, { color: '#10B981' }]}>{formatPrice()}</Text>
          </View>
          <View style={styles.priceRow}>
            <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>Duración:</Text>
            <Text style={[styles.durationValue, { color: colors.text }]}>{formatDuration()}</Text>
          </View>
        </View>

        {/* Requirements */}
        {service.requirements && service.requirements.length > 0 && (
          <View style={[styles.section, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Incluye:</Text>
            {service.requirements.map((requirement, index) => (
              <View key={index} style={styles.requirementRow}>
                <Text style={styles.checkIcon}>✅</Text>
                <Text style={[styles.requirementText, { color: colors.textSecondary }]}>
                  {requirement}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Tags */}
        {service.tags && service.tags.length > 0 && (
          <View style={[styles.section, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Relacionado con:</Text>
            <View style={styles.tagsContainer}>
              {service.tags.map((tag, index) => (
                <View key={index} style={[styles.tag, { backgroundColor: colors.backgroundGray, borderColor: colors.border }]}>
                  <Text style={[styles.tagText, { color: colors.textSecondary }]}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>

      {/* Bottom Action */}
      <View style={[styles.bottomAction, { backgroundColor: colors.background, borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.requestButton, { backgroundColor: colors.primary }]}
          onPress={handleRequestService}
        >
          <Text style={styles.requestButtonText}>Solicitar este Servicio</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  serviceHeader: {
    flexDirection: 'row',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    alignItems: 'center',
  },
  serviceIcon: {
    fontSize: 48,
    marginRight: 15,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 5,
  },
  serviceDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 10,
  },
  popularBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  priceSection: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  priceLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  durationValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
  },
  requirementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  requirementText: {
    fontSize: 14,
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  bottomAction: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
  },
  requestButton: {
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  requestButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
})
