import React from 'react'
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity 
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'

export default function PaymentsScreen({ navigation }: any) {
  const { colors } = useTheme()

  const paymentMethods = [
    {
      id: '1',
      type: 'card',
      name: 'Visa •••• 4242',
      icon: 'card-outline',
      isDefault: true
    },
    {
      id: '2',
      type: 'paypal',
      name: 'PayPal',
      icon: 'logo-paypal',
      isDefault: false
    }
  ]

  const transactions = [
    {
      id: '1',
      title: 'Pago por limpieza de casa',
      amount: '$150.000',
      date: '15 Ene 2024',
      status: 'completed',
      type: 'payment'
    },
    {
      id: '2',
      title: 'Recarga de saldo',
      amount: '+$200.000',
      date: '10 Ene 2024',
      status: 'completed',
      type: 'deposit'
    }
  ]

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Pagos y Facturación</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Balance */}
        <View style={[styles.balanceCard, { backgroundColor: colors.primary }]}>
          <Text style={styles.balanceLabel}>Saldo disponible</Text>
          <Text style={styles.balanceAmount}>$450.000</Text>
          <TouchableOpacity style={[styles.addFundsButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
            <Ionicons name="add" size={20} color="#FFFFFF" />
            <Text style={styles.addFundsText}>Agregar fondos</Text>
          </TouchableOpacity>
        </View>

        {/* Payment Methods */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Métodos de pago</Text>
            <TouchableOpacity>
              <Text style={[styles.addButton, { color: colors.primary }]}>Agregar</Text>
            </TouchableOpacity>
          </View>
          
          {paymentMethods.map((method) => (
            <TouchableOpacity key={method.id} style={[styles.paymentMethod, { borderBottomColor: colors.border }]}>
              <View style={[styles.methodIcon, { backgroundColor: colors.primaryLight }]}>
                <Ionicons name={method.icon as any} size={24} color={colors.primary} />
              </View>
              <View style={styles.methodInfo}>
                <Text style={[styles.methodName, { color: colors.text }]}>{method.name}</Text>
                {method.isDefault && (
                  <Text style={[styles.defaultLabel, { color: colors.primary }]}>Predeterminado</Text>
                )}
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Transaction History */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Historial de transacciones</Text>
          
          {transactions.map((transaction) => (
            <TouchableOpacity key={transaction.id} style={[styles.transaction, { borderBottomColor: colors.border }]}>
              <View style={[
                styles.transactionIcon, 
                { backgroundColor: transaction.type === 'payment' ? colors.error + '20' : colors.success + '20' }
              ]}>
                <Ionicons 
                  name={transaction.type === 'payment' ? 'arrow-up' : 'arrow-down'} 
                  size={20} 
                  color={transaction.type === 'payment' ? colors.error : colors.success} 
                />
              </View>
              <View style={styles.transactionInfo}>
                <Text style={[styles.transactionTitle, { color: colors.text }]}>{transaction.title}</Text>
                <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>{transaction.date}</Text>
              </View>
              <Text style={[
                styles.transactionAmount, 
                { color: transaction.type === 'payment' ? colors.error : colors.success }
              ]}>
                {transaction.amount}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  balanceCard: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  balanceLabel: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    marginBottom: 8,
  },
  balanceAmount: {
    color: '#FFFFFF',
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 16,
  },
  addFundsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 4,
  },
  addFundsText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    margin: 20,
    borderRadius: 16,
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  addButton: {
    fontSize: 16,
    fontWeight: '500',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  methodInfo: {
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  defaultLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  transaction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
})
