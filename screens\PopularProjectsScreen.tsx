import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
} from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { useTheme } from "../contexts/ThemeContext"
import { useServices } from "../contexts/ServicesContext"

export default function PopularProjectsScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { categories, getAllServices } = useServices()
  const [searchQuery, setSearchQuery] = useState("")

  // Get popular projects category
  const popularCategory = categories.find(cat => cat.id === "populares")
  const popularServices = popularCategory?.services || []

  // Get other popular services from all categories
  const allServices = getAllServices()
  const otherPopularServices = allServices
    .filter(service => service.popular && service.category !== "populares")
    .slice(0, 6)

  const filteredServices = searchQuery
    ? allServices.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : []

  const handleServiceSelect = (service: any) => {
    navigation.navigate("TaskFlow", { 
      selectedService: service,
      step: "location"
    })
  }

  const formatPrice = (min: number, max: number, unit: string) => {
    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(num)
    }
    
    if (unit === "servicio") {
      return `${formatNumber(min)} - ${formatNumber(max)}`
    }
    return `${formatNumber(min)} - ${formatNumber(max)} por ${unit}`
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          ¿Qué necesitas hacer?
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface }]}>
        <Ionicons name="search" size={20} color={colors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: colors.text }]}
          placeholder="Buscar servicios..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery("")}>
            <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {searchQuery.length > 0 ? (
          /* Search Results */
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Resultados de búsqueda
            </Text>
            {filteredServices.length === 0 ? (
              <Text style={[styles.noResults, { color: colors.textSecondary }]}>
                No se encontraron servicios
              </Text>
            ) : (
              filteredServices.map((service) => (
                <TouchableOpacity
                  key={service.id}
                  style={[styles.serviceCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                  onPress={() => handleServiceSelect(service)}
                >
                  <Text style={styles.serviceIcon}>{service.icon}</Text>
                  <View style={styles.serviceInfo}>
                    <Text style={[styles.serviceName, { color: colors.text }]}>
                      {service.name}
                    </Text>
                    <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
                      {service.description}
                    </Text>
                    <Text style={[styles.servicePrice, { color: colors.primary }]}>
                      {formatPrice(service.priceRange.min, service.priceRange.max, service.priceRange.unit)}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              ))
            )}
          </View>
        ) : (
          <>
            {/* Popular Projects */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Proyectos Populares
              </Text>
              <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
                Los servicios más solicitados
              </Text>
              
              <View style={styles.servicesGrid}>
                {popularServices.map((service) => (
                  <TouchableOpacity
                    key={service.id}
                    style={[styles.popularCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                    onPress={() => handleServiceSelect(service)}
                  >
                    <Text style={styles.popularIcon}>{service.icon}</Text>
                    <Text style={[styles.popularName, { color: colors.text }]}>
                      {service.name}
                    </Text>
                    <Text style={[styles.popularPrice, { color: colors.primary }]}>
                      Desde {formatPrice(service.priceRange.min, service.priceRange.min, service.priceRange.unit).split(' - ')[0]}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Other Popular Services */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                También Popular
              </Text>
              
              {otherPopularServices.map((service) => (
                <TouchableOpacity
                  key={service.id}
                  style={[styles.serviceCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                  onPress={() => handleServiceSelect(service)}
                >
                  <Text style={styles.serviceIcon}>{service.icon}</Text>
                  <View style={styles.serviceInfo}>
                    <Text style={[styles.serviceName, { color: colors.text }]}>
                      {service.name}
                    </Text>
                    <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
                      {service.description}
                    </Text>
                    <Text style={[styles.servicePrice, { color: colors.primary }]}>
                      {formatPrice(service.priceRange.min, service.priceRange.max, service.priceRange.unit)}
                    </Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              ))}
            </View>

            {/* Browse All Categories */}
            <View style={styles.section}>
              <TouchableOpacity
                style={[styles.browseAllButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => navigation.navigate("AllCategories")}
              >
                <Ionicons name="grid-outline" size={24} color={colors.primary} />
                <Text style={[styles.browseAllText, { color: colors.primary }]}>
                  Ver todas las categorías
                </Text>
                <Ionicons name="chevron-forward" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 20,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    marginBottom: 20,
  },
  servicesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 15,
  },
  popularCard: {
    width: "47%",
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
    minHeight: 120,
  },
  popularIcon: {
    fontSize: 32,
    marginBottom: 10,
  },
  popularName: {
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 8,
  },
  popularPrice: {
    fontSize: 12,
    fontWeight: "500",
  },
  serviceCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 10,
    gap: 15,
  },
  serviceIcon: {
    fontSize: 24,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    marginBottom: 6,
  },
  servicePrice: {
    fontSize: 14,
    fontWeight: "500",
  },
  browseAllButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    gap: 15,
  },
  browseAllText: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
  },
  noResults: {
    textAlign: "center",
    fontSize: 16,
    marginTop: 40,
  },
})
