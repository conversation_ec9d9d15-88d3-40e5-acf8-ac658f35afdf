import React, { useState, useRef } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Modal,
  Alert,
} from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { useTheme } from "../contexts/ThemeContext"

// Mock professional data
const mockProfessionals = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    rating: 5.0,
    reviewCount: 194,
    completedTasks: 350,
    categoryTasks: 322,
    hourlyRate: 41.29,
    experience: "Tengo 4 años de experiencia en instalación de muebles de oficina, todo tipo de camas, sillas, instalación comercial...",
    skills: ["Furniture Assembly", "IKEA Assembly", "Office Setup"],
    responseTime: "Responde en 1 hora",
    availability: "Disponible hoy",
    verified: true,
  },
  {
    id: "2",
    name: "<PERSON>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    rating: 5.0,
    reviewCount: 237,
    completedTasks: 382,
    categoryTasks: 380,
    hourlyRate: 43.36,
    experience: "Especialista en ensamble de muebles con más de 5 años de experiencia. Trabajo rápido y limpio garantizado.",
    skills: ["Furniture Assembly", "Home Organization", "Moving Help"],
    responseTime: "Responde en 30 min",
    availability: "Disponible mañana",
    verified: true,
  },
  {
    id: "3",
    name: "Jorge R.",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    rating: 4.8,
    reviewCount: 156,
    completedTasks: 298,
    categoryTasks: 245,
    hourlyRate: 38.50,
    experience: "Carpintero profesional con experiencia en ensamble de todo tipo de muebles. Herramientas propias incluidas.",
    skills: ["Carpentry", "Furniture Assembly", "Repairs"],
    responseTime: "Responde en 2 horas",
    availability: "Disponible esta semana",
    verified: false,
  },
]

export default function TaskResultsScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { taskData, service } = route.params
  const [sortBy, setSortBy] = useState("recommended")
  const [filters, setFilters] = useState({
    date: "within_week",
    time: "flexible",
    priceRange: "$10-$150/hr",
  })
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false)
  const [selectedProfessional, setSelectedProfessional] = useState<any>(null)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedTime, setSelectedTime] = useState('8:30am')
  const [showTimeDropdown, setShowTimeDropdown] = useState(false)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [holidays, setHolidays] = useState<any[]>([])

  // Dropdown states
  const [showDateFilter, setShowDateFilter] = useState(false)
  const [showTimeFilter, setShowTimeFilter] = useState(false)
  const [showPriceFilter, setShowPriceFilter] = useState(false)
  const [showSortFilter, setShowSortFilter] = useState(false)

  // Button refs for positioning
  const dateButtonRef = useRef(null)
  const timeButtonRef = useRef(null)
  const priceButtonRef = useRef(null)
  const sortButtonRef = useRef(null)

  // Button positions
  const [buttonPositions, setButtonPositions] = useState({
    date: { x: 0, y: 0, width: 0, height: 0 },
    time: { x: 0, y: 0, width: 0, height: 0 },
    price: { x: 0, y: 0, width: 0, height: 0 },
    sort: { x: 0, y: 0, width: 0, height: 0 },
  })

  // Filter options
  const dateOptions = ["Hoy", "Mañana", "Esta semana", "Próxima semana", "Cualquier día"]
  const timeOptions = ["Mañana", "Tarde", "Noche", "Soy flexible"]
  const priceOptions = ["Menor a mayor", "Mayor a menor", "Cualquier precio"]
  const sortOptions = ["Recomendado", "Mejor calificado", "Precio menor", "Precio mayor", "Más experiencia"]

  const closeAllDropdowns = () => {
    setShowDateFilter(false)
    setShowTimeFilter(false)
    setShowPriceFilter(false)
    setShowSortFilter(false)
  }

  const measureButton = (ref: any, type: string) => {
    if (ref.current) {
      ref.current.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        setButtonPositions(prev => ({
          ...prev,
          [type]: { x: pageX, y: pageY, width, height }
        }))
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price * 1000) // Convert to COP (approximate)
  }

  // Funciones del calendario
  const getMonthName = (date: Date) => {
    const months = [
      'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ]
    return months[date.getMonth()]
  }

  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    const today = new Date()

    // Días del mes anterior para llenar la primera semana
    const prevMonth = new Date(year, month - 1, 0)
    const daysInPrevMonth = prevMonth.getDate()

    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const day = daysInPrevMonth - i
      const date = new Date(year, month - 1, day)
      const isPast = date < today && date.toDateString() !== today.toDateString()
      const isHolidayDate = isHoliday(date)

      days.push({
        date: date,
        day: day,
        isPast: isPast,
        isAvailable: false, // Días del mes anterior no disponibles
        isToday: date.toDateString() === today.toDateString(),
        isHoliday: isHolidayDate,
        holidayName: isHolidayDate ? getHolidayName(date) : null,
        isCurrentMonth: false
      })
    }

    // Días del mes actual
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      const isPast = date < today && date.toDateString() !== today.toDateString()
      const isHolidayDate = isHoliday(date)
      const isAvailable = !isPast && !isHolidayDate && Math.random() > 0.3 // 70% de días disponibles (no festivos)

      days.push({
        date: date,
        day: day,
        isPast: isPast,
        isAvailable: isAvailable,
        isToday: date.toDateString() === today.toDateString(),
        isHoliday: isHolidayDate,
        holidayName: isHolidayDate ? getHolidayName(date) : null,
        isCurrentMonth: true
      })
    }

    // Días del mes siguiente para completar la última semana
    const totalCells = Math.ceil(days.length / 7) * 7
    let nextMonthDay = 1

    for (let i = days.length; i < totalCells; i++) {
      const date = new Date(year, month + 1, nextMonthDay)
      const isPast = date < today && date.toDateString() !== today.toDateString()
      const isHolidayDate = isHoliday(date)

      days.push({
        date: date,
        day: nextMonthDay,
        isPast: isPast,
        isAvailable: false, // Días del mes siguiente no disponibles
        isToday: date.toDateString() === today.toDateString(),
        isHoliday: isHolidayDate,
        holidayName: isHolidayDate ? getHolidayName(date) : null,
        isCurrentMonth: false
      })

      nextMonthDay++
    }

    return days
  }

  const changeMonth = (direction: number) => {
    const newMonth = new Date(currentMonth)
    newMonth.setMonth(currentMonth.getMonth() + direction)
    setCurrentMonth(newMonth)
  }

  const isDateSelected = (date: Date) => {
    return selectedDate.toDateString() === date.toDateString()
  }

  // Función para obtener festivos de Colombia
  const fetchHolidays = async (year: number) => {
    try {
      const response = await fetch(`https://date.nager.at/api/v3/PublicHolidays/${year}/CO`)
      const data = await response.json()
      return data
    } catch (error) {
      console.log('Error fetching holidays:', error)
      // Fallback con algunos festivos principales de Colombia
      return [
        { date: `${year}-01-01`, name: 'Año Nuevo', localName: 'Año Nuevo' },
        { date: `${year}-05-01`, name: 'Día del Trabajo', localName: 'Día del Trabajo' },
        { date: `${year}-07-20`, name: 'Día de la Independencia', localName: 'Día de la Independencia' },
        { date: `${year}-08-07`, name: 'Batalla de Boyacá', localName: 'Batalla de Boyacá' },
        { date: `${year}-12-08`, name: 'Inmaculada Concepción', localName: 'Inmaculada Concepción' },
        { date: `${year}-12-25`, name: 'Navidad', localName: 'Navidad' }
      ]
    }
  }

  // Verificar si una fecha es festivo
  const isHoliday = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return holidays.some(holiday => holiday.date === dateString)
  }

  // Obtener nombre del festivo
  const getHolidayName = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    const holiday = holidays.find(holiday => holiday.date === dateString)
    return holiday ? holiday.localName || holiday.name : null
  }

  // Cargar festivos cuando cambie el año
  React.useEffect(() => {
    const loadHolidays = async () => {
      const currentYear = currentMonth.getFullYear()
      const holidayData = await fetchHolidays(currentYear)
      setHolidays(holidayData)
    }
    loadHolidays()
  }, [currentMonth.getFullYear()])

  const renderDropdown = (options: string[], selectedValue: string, onSelect: (value: string) => void, onClose: () => void, dropdownType: string) => {
    // Posiciones aproximadas para cada dropdown - debajo y pegaditos a los botones
    const getDropdownPosition = () => {
      switch(dropdownType) {
        case 'date': return { top: 125, left: 20 }
        case 'time': return { top: 125, left: 110 } // Más a la derecha para alinearse mejor con el botón Hora
        case 'price': return { top: 125, left: 180 } // Más a la derecha para alinearse con el botón Precio
        case 'sort': return { top: 175, left: 115 } // Más abajo y un toque a la derecha como el de fecha
        default: return { top: 125, left: 20 }
      }
    }

    const position = getDropdownPosition()

    return (
      <Modal
        visible={true}
        transparent={true}
        animationType="none"
        onRequestClose={onClose}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={onClose}
        >
          <View style={[
            styles.dropdown,
            {
              backgroundColor: colors.background,
              borderColor: colors.border,
              position: 'absolute',
              top: position.top,
              left: position.left,
              minWidth: 120,
            }
          ]}>
            {options.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dropdownItem,
                  index < options.length - 1 && { borderBottomColor: colors.border, borderBottomWidth: 0.5 },
                  selectedValue === option && { backgroundColor: colors.primary + '10' }
                ]}
                onPress={() => {
                  onSelect(option)
                  onClose()
                }}
              >
                <Text style={[
                  styles.dropdownText,
                  { color: colors.text },
                  selectedValue === option && { fontWeight: '600', color: colors.primary }
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    )
  }



  const handleSelectProfessional = (professional: any) => {
    setSelectedProfessional(professional)
    setShowAvailabilityModal(true)
  }

  const handleConfirmBooking = () => {
    setShowAvailabilityModal(false)

    // Publicar el servicio requerido
    Alert.alert(
      "¡Servicio Publicado!",
      `Tu solicitud de servicio ha sido publicada exitosamente.\n\nFecha: ${selectedDate.toLocaleDateString('es-ES', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })}\nHora: ${selectedTime}\nProfesional: ${selectedProfessional?.name}\n\nLos profesionales interesados podrán contactarte pronto.`,
      [
        {
          text: "Ver mis solicitudes",
          onPress: () => {
            // Navegar a pantalla de solicitudes/perfil
            navigation.navigate('Profile')
          }
        },
        {
          text: "Continuar buscando",
          style: "cancel"
        }
      ]
    )
  }

  const renderProfessionalCard = (professional: any) => (
    <View key={professional.id} style={[styles.professionalCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      {/* Header with price */}
      <View style={styles.cardHeader}>
        <View style={styles.professionalInfo}>
          <Image source={{ uri: professional.avatar }} style={styles.avatar} />
          <View style={styles.nameSection}>
            <View style={styles.nameRow}>
              <Text style={[styles.professionalName, { color: colors.text }]}>
                {professional.name}
              </Text>
              {professional.verified && (
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
              )}
            </View>
            <View style={styles.ratingRow}>
              <Ionicons name="star" size={14} color="#FFD700" />
              <Text style={[styles.rating, { color: colors.text }]}>
                {professional.rating} ({professional.reviewCount} reseñas)
              </Text>
            </View>
          </View>
        </View>
        <Text style={[styles.hourlyRate, { color: colors.text }]}>
          {formatPrice(professional.hourlyRate)}/hr
        </Text>
      </View>

      {/* Stats */}
      <View style={styles.statsRow}>
        <Text style={[styles.statsText, { color: colors.textSecondary }]}>
          {professional.completedTasks} {service.name} tareas
        </Text>
        <Text style={[styles.statsText, { color: colors.textSecondary }]}>
          {professional.categoryTasks} tareas de ensamble en total
        </Text>
      </View>

      {/* Experience */}
      <Text style={[styles.experience, { color: colors.text }]} numberOfLines={2}>
        {professional.experience}
      </Text>
      
      <TouchableOpacity>
        <Text style={[styles.readMore, { color: colors.primary }]}>Leer más</Text>
      </TouchableOpacity>

      {/* Availability */}
      <View style={styles.availabilityRow}>
        <View style={[styles.availabilityBadge, { backgroundColor: colors.success + "20" }]}>
          <Text style={[styles.availabilityText, { color: colors.success }]}>
            {professional.availability}
          </Text>
        </View>
        <Text style={[styles.responseTime, { color: colors.textSecondary }]}>
          {professional.responseTime}
        </Text>
      </View>

      {/* Action Button */}
      <TouchableOpacity
        style={[styles.selectButton, { backgroundColor: colors.primary }]}
        onPress={() => handleSelectProfessional(professional)}
      >
        <Text style={styles.selectButtonText}>Seleccionar y Continuar</Text>
      </TouchableOpacity>
    </View>
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {service.name}
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            {taskData.location.address}
          </Text>
        </View>
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressStep, { backgroundColor: colors.primary }]} />
          <View style={styles.progressSeparator} />
          <View style={[styles.progressStep, { backgroundColor: colors.primary }]} />
          <View style={styles.progressSeparator} />
          <View style={[styles.progressStep, { backgroundColor: colors.primary }]} />
          <View style={styles.progressSeparator} />
          <View style={[styles.progressStep, { backgroundColor: '#E0E0E0' }]} />
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filtersRow}>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            ref={dateButtonRef}
            style={[styles.filterButton, { borderColor: colors.border }]}
            onPress={() => {
              closeAllDropdowns()
              setShowDateFilter(!showDateFilter)
            }}
          >
            <Text style={[styles.filterButtonText, { color: colors.text }]}>Fecha</Text>
            <Ionicons name="chevron-down" size={14} color={colors.textSecondary} />
          </TouchableOpacity>
          {showDateFilter && renderDropdown(dateOptions, filters.date, (value) => setFilters({...filters, date: value}), () => setShowDateFilter(false), 'date')}
        </View>

        <View style={styles.filterContainer}>
          <TouchableOpacity
            ref={timeButtonRef}
            style={[styles.filterButton, { borderColor: colors.border }]}
            onPress={() => {
              closeAllDropdowns()
              setShowTimeFilter(!showTimeFilter)
            }}
          >
            <Text style={[styles.filterButtonText, { color: colors.text }]}>Hora</Text>
            <Ionicons name="chevron-down" size={14} color={colors.textSecondary} />
          </TouchableOpacity>
          {showTimeFilter && renderDropdown(timeOptions, filters.time, (value) => setFilters({...filters, time: value}), () => setShowTimeFilter(false), 'time')}
        </View>

        <View style={styles.filterContainer}>
          <TouchableOpacity
            ref={priceButtonRef}
            style={[styles.filterButton, { borderColor: colors.border }]}
            onPress={() => {
              closeAllDropdowns()
              setShowPriceFilter(!showPriceFilter)
            }}
          >
            <Text style={[styles.filterButtonText, { color: colors.text }]}>Precio</Text>
            <Ionicons name="chevron-down" size={14} color={colors.textSecondary} />
          </TouchableOpacity>
          {showPriceFilter && renderDropdown(priceOptions, filters.priceRange, (value) => setFilters({...filters, priceRange: value}), () => setShowPriceFilter(false), 'price')}
        </View>
      </View>

      {/* Sort Dropdown */}
      <View style={styles.sortContainer}>
        <Text style={[styles.sortLabel, { color: colors.text }]}>Ordenado por:</Text>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            ref={sortButtonRef}
            style={[styles.sortDropdown, { borderColor: colors.border }]}
            onPress={() => {
              closeAllDropdowns()
              setShowSortFilter(!showSortFilter)
            }}
          >
            <Text style={[styles.sortText, { color: colors.text }]}>Recomendado</Text>
            <Ionicons name="chevron-down" size={14} color={colors.textSecondary} />
          </TouchableOpacity>
          {showSortFilter && renderDropdown(sortOptions, sortBy, (value) => setSortBy(value), () => setShowSortFilter(false), 'sort')}
        </View>
      </View>





      {/* Professionals List */}
      <ScrollView
        style={styles.professionalsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        {mockProfessionals.map(renderProfessionalCard)}
      </ScrollView>

      {/* Availability Modal */}
      <Modal
        visible={showAvailabilityModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAvailabilityModal(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <TouchableOpacity onPress={() => setShowAvailabilityModal(false)}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Disponibilidad</Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            {selectedProfessional && (
              <View style={[styles.professionalHeader, { borderBottomColor: colors.border }]}>
                <Image source={{ uri: selectedProfessional.avatar }} style={styles.modalAvatar} />
                <View>
                  <Text style={[styles.modalProfessionalName, { color: colors.text }]}>
                    {selectedProfessional.name}
                  </Text>
                  <Text style={[styles.modalProfessionalRate, { color: colors.textSecondary }]}>
                    {formatPrice(selectedProfessional.hourlyRate)}/hr
                  </Text>
                </View>
              </View>
            )}

            <View style={styles.calendarSection}>
              {/* Calendar Header */}
              <View style={styles.calendarHeader}>
                <TouchableOpacity onPress={() => changeMonth(-1)} style={styles.monthNavButton}>
                  <Ionicons name="chevron-back" size={20} color={colors.primary} />
                </TouchableOpacity>

                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {getMonthName(currentMonth)} {currentMonth.getFullYear()}
                </Text>

                <TouchableOpacity onPress={() => changeMonth(1)} style={styles.monthNavButton}>
                  <Ionicons name="chevron-forward" size={20} color={colors.primary} />
                </TouchableOpacity>
              </View>

              {/* Calendar Grid */}
              <View style={styles.calendarGrid}>
                <View style={styles.weekDays}>
                  {['DOM', 'LUN', 'MAR', 'MIÉ', 'JUE', 'VIE', 'SÁB'].map((day) => (
                    <Text key={day} style={[styles.weekDay, { color: colors.textSecondary }]}>{day}</Text>
                  ))}
                </View>

                <View style={styles.datesGrid}>
                  {generateCalendarDays().map((dayData, index) => {
                    const { date, day, isPast, isAvailable, isToday, isHoliday, holidayName, isCurrentMonth } = dayData
                    const isSelected = isDateSelected(date)

                    // Determinar color del texto
                    let textColor = '#CCCCCC' // Por defecto gris claro
                    let fontWeight = '400'

                    if (isSelected) {
                      textColor = 'white'
                      fontWeight = '600'
                    } else if (!isCurrentMonth) {
                      textColor = '#CCCCCC' // Días de otros meses en gris claro
                      fontWeight = '400'
                    } else if (isPast) {
                      textColor = '#CCCCCC' // Días pasados en gris claro
                      fontWeight = '400'
                    } else if (isHoliday) {
                      textColor = '#FF6B6B' // Festivos en rojo
                      fontWeight = '700'
                    } else if (isAvailable) {
                      textColor = '#000000' // Disponibles en negro
                      fontWeight = '600'
                    } else {
                      textColor = '#999999' // No disponibles en gris
                      fontWeight = '400'
                    }

                    return (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.dateCell,
                          isSelected && { backgroundColor: colors.primary },
                          isToday && !isSelected && { borderWidth: 2, borderColor: colors.primary }
                        ]}
                        onPress={() => !isPast && isCurrentMonth && setSelectedDate(date)}
                        disabled={isPast || !isCurrentMonth}
                      >
                        <Text style={[
                          styles.dateText,
                          {
                            color: textColor,
                            fontWeight: fontWeight
                          }
                        ]}>
                          {day}
                        </Text>
                      </TouchableOpacity>
                    )
                  })}
                </View>

                {/* Calendar Legend */}
                <View style={styles.calendarLegend}>
                  <View style={styles.legendItem}>
                    <Text style={[styles.legendText, { color: '#000000', fontWeight: '600' }]}>Negro</Text>
                    <Text style={[styles.legendSubtext, { color: colors.textSecondary }]}>Disponible</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <Text style={[styles.legendText, { color: '#FF6B6B', fontWeight: '700' }]}>Rojo</Text>
                    <Text style={[styles.legendSubtext, { color: colors.textSecondary }]}>Festivo</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <Text style={[styles.legendText, { color: '#999999', fontWeight: '400' }]}>Gris</Text>
                    <Text style={[styles.legendSubtext, { color: colors.textSecondary }]}>No disponible</Text>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.timeSection}>
              <TouchableOpacity
                style={[styles.timeSelector, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => setShowTimeDropdown(!showTimeDropdown)}
              >
                <Text style={[styles.timeText, { color: colors.text }]}>{selectedTime}</Text>
                <Ionicons name="chevron-down" size={20} color={colors.textSecondary} />
              </TouchableOpacity>

              {/* Time Dropdown */}
              {showTimeDropdown && (
                <ScrollView
                  style={[styles.timeDropdown, { backgroundColor: colors.surface, borderColor: colors.border }]}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                >
                  {[
                    '8:00am', '8:30am', '9:00am', '9:30am', '10:00am', '10:30am',
                    '11:00am', '11:30am', '12:00pm', '12:30pm', '1:00pm', '1:30pm',
                    '2:00pm', '2:30pm', '3:00pm', '3:30pm', '4:00pm', '4:30pm',
                    '5:00pm', '5:30pm', '6:00pm', '6:30pm', '7:00pm', '7:30pm'
                  ].map((time) => (
                    <TouchableOpacity
                      key={time}
                      style={[styles.timeOption, selectedTime === time && { backgroundColor: colors.primary }]}
                      onPress={() => {
                        setSelectedTime(time)
                        setShowTimeDropdown(false)
                      }}
                    >
                      <Text style={[
                        styles.timeOptionText,
                        { color: selectedTime === time ? 'white' : colors.text }
                      ]}>
                        {time}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              )}
            </View>

            <View style={[styles.requestSummary, { backgroundColor: colors.surface }]}>
              <Text style={[styles.requestLabel, { color: colors.textSecondary }]}>Solicitar para:</Text>
              <Text style={[styles.requestDate, { color: colors.text }]}>
                {selectedDate.toLocaleDateString('es-ES', {
                  month: 'short',
                  day: 'numeric'
                })}, {selectedTime}
              </Text>
            </View>
          </ScrollView>

          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: colors.primary }]}
              onPress={handleConfirmBooking}
            >
              <Text style={styles.confirmButtonText}>Publicar Servicio</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    gap: 15,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  progressBar: {
    flexDirection: "row",
    alignItems: "center",
    height: 6,
  },
  progressStep: {
    flex: 1,
    height: 6,
    borderRadius: 3,
  },
  progressSeparator: {
    width: 6,
    height: 1,
    backgroundColor: "#D0D0D0",
    borderRadius: 0.5,
    marginHorizontal: 2,
  },
  filtersRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
    gap: 8,
    zIndex: 9999,
    position: 'relative',
  },
  filterContainer: {
    position: 'relative',
    zIndex: 9999,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1,
    gap: 4,
  },
  filterButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  dropdown: {
    borderRadius: 6,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 15,
  },
  dropdownItem: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    minHeight: 40,
    justifyContent: 'center',
  },
  dropdownText: {
    fontSize: 13,
    textAlign: 'left',
    flexWrap: 'nowrap',
  },
  sortContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 8,
    gap: 8,
  },
  sortLabel: {
    fontSize: 13,
    fontWeight: "500",
  },
  sortDropdown: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1,
    gap: 6,
  },
  sortText: {
    fontSize: 13,
  },
  professionalsList: {
    flex: 1,
    paddingHorizontal: 20,
    zIndex: 1,
  },
  professionalCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 15,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  professionalInfo: {
    flexDirection: "row",
    alignItems: "flex-start",
    flex: 1,
    gap: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  nameSection: {
    flex: 1,
  },
  nameRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginBottom: 4,
  },
  professionalName: {
    fontSize: 16,
    fontWeight: "600",
  },
  ratingRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rating: {
    fontSize: 14,
  },
  hourlyRate: {
    fontSize: 16,
    fontWeight: "600",
  },
  statsRow: {
    marginBottom: 12,
  },
  statsText: {
    fontSize: 12,
    marginBottom: 2,
  },
  experience: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  readMore: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 12,
  },
  availabilityRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 15,
  },
  availabilityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: "500",
  },
  responseTime: {
    fontSize: 12,
  },
  selectButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  selectButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  professionalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    gap: 15,
  },
  modalAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  modalProfessionalName: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalProfessionalRate: {
    fontSize: 14,
    marginTop: 2,
  },
  calendarSection: {
    paddingVertical: 20,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  monthNavButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#F0F8FF',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
  calendarGrid: {
    marginTop: 15,
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    padding: 15,
  },
  weekDays: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  weekDay: {
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
    width: 40,
    color: '#666',
  },
  datesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 5,
  },
  dateCell: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 3,
    backgroundColor: 'transparent',
  },
  dateText: {
    fontSize: 15,
    fontWeight: '500',
  },

  calendarLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  legendItem: {
    alignItems: 'center',
    gap: 2,
  },
  legendText: {
    fontSize: 14,
    textAlign: 'center',
  },
  legendSubtext: {
    fontSize: 11,
    textAlign: 'center',
  },
  timeSection: {
    paddingVertical: 20,
  },
  timeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  timeText: {
    fontSize: 16,
  },
  timeDropdown: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    maxHeight: 200,
    borderRadius: 8,
    borderWidth: 1,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  timeOption: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  timeOptionText: {
    fontSize: 16,
  },
  requestSummary: {
    padding: 15,
    borderRadius: 8,
    marginVertical: 20,
  },
  requestLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  requestDate: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalFooter: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
  },
  confirmButton: {
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
})
