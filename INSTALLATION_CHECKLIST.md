# ✅ CHECKLIST DE INSTALACIÓN - TaskApp LATAM

## 📋 HERRAMIENTAS REQUERIDAS

### 💻 BÁSICAS (Obligatorias)
- [ ] **Node.js v18+ LTS** - Runtime JavaScript
- [ ] **Git** - Control de versiones  
- [ ] **Visual Studio Code** - Editor principal
- [ ] **Google Chrome** - Debugging y desarrollo
- [ ] **Docker Desktop** - Containerización

### 📱 MOBILE DEVELOPMENT
- [ ] **Expo CLI** - Herramientas React Native
- [ ] **Android Studio** - Emulador Android
- [ ] **Expo Go** (en tu teléfono) - Testing real

### 🗄️ BASES DE DATOS
- [ ] **PostgreSQL** - Base de datos (via Docker)
- [ ] **Redis** - Cache (via Docker)
- [ ] **pgAdmin** - Interfaz gráfica (via Docker)

### 🧪 TESTING Y APIS
- [ ] **Postman** o **Insomnia** - Testing APIs
- [ ] **React Native Debugger** - Debugging RN

## 🔧 COMANDOS DE INSTALACIÓN

### 💻 Windows 10/11

#### 1. Node.js
```bash
# Descargar desde: https://nodejs.org/
# Versión recomendada: 18.18.0 LTS
# Verificar instalación:
node --version
npm --version
```

#### 2. Git
```bash
# Descargar desde: https://git-scm.com/
# Configurar después de instalar:
git config --global user.name "Tu Nombre"
git config --global user.email "<EMAIL>"
```

#### 3. Visual Studio Code
```bash
# Descargar desde: https://code.visualstudio.com/
# Extensiones requeridas:
# - React Native Tools
# - TypeScript Hero
# - Prettier
# - ES7+ React/Redux/React-Native snippets
# - Docker
```

#### 4. Docker Desktop
```bash
# Descargar desde: https://www.docker.com/products/docker-desktop/
# Requiere WSL2 en Windows
# Verificar instalación:
docker --version
docker-compose --version
```

#### 5. Expo CLI
```bash
# Instalar globalmente:
npm install -g @expo/cli
npm install -g @expo/eas-cli

# Verificar instalación:
expo --version
eas --version
```

#### 6. Android Studio
```bash
# Descargar desde: https://developer.android.com/studio
# Instalar Android SDK
# Configurar emulador Android
# Agregar al PATH las herramientas de Android
```

### 🍎 macOS

#### 1. Homebrew (Package Manager)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### 2. Node.js
```bash
brew install node@18
node --version
npm --version
```

#### 3. Git
```bash
brew install git
git config --global user.name "Tu Nombre"
git config --global user.email "<EMAIL>"
```

#### 4. Docker Desktop
```bash
# Descargar desde: https://www.docker.com/products/docker-desktop/
# O con Homebrew:
brew install --cask docker
```

#### 5. Expo CLI
```bash
npm install -g @expo/cli @expo/eas-cli
```

#### 6. Xcode (para iOS)
```bash
# Instalar desde App Store
# Instalar Command Line Tools:
xcode-select --install
```

### 🐧 Linux (Ubuntu/Debian)

#### 1. Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
node --version
npm --version
```

#### 2. Git
```bash
sudo apt update
sudo apt install git
git config --global user.name "Tu Nombre"
git config --global user.email "<EMAIL>"
```

#### 3. Docker
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo usermod -aG docker $USER
# Reiniciar sesión después
```

#### 4. Expo CLI
```bash
npm install -g @expo/cli @expo/eas-cli
```

## 📱 CONFIGURACIÓN MOBILE

### 📲 Expo Go (En tu teléfono)
- [ ] **Android**: Descargar desde Google Play Store
- [ ] **iOS**: Descargar desde App Store
- [ ] Crear cuenta en Expo
- [ ] Iniciar sesión en la app

### 🤖 Android Studio Setup
- [ ] Instalar Android Studio
- [ ] Configurar Android SDK (API 33+)
- [ ] Crear AVD (Android Virtual Device)
- [ ] Configurar variables de entorno:
  ```bash
  ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
  PATH=%PATH%;%ANDROID_HOME%\platform-tools
  ```

### 🍎 iOS Setup (Solo Mac)
- [ ] Instalar Xcode
- [ ] Instalar Command Line Tools
- [ ] Configurar simulador iOS

## 🐳 DOCKER CONFIGURATION

### 📄 Archivos Docker Requeridos

#### docker-compose.yml
```yaml
version: '3.8'
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: taskapp
      POSTGRES_USER: taskapp_user
      POSTGRES_PASSWORD: taskapp_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - db

volumes:
  postgres_data:
  redis_data:
```

### 🚀 Comandos Docker Básicos
```bash
# Levantar servicios
docker-compose up -d

# Ver servicios corriendo
docker-compose ps

# Ver logs
docker-compose logs

# Parar servicios
docker-compose down

# Limpiar todo
docker-compose down -v
```

## 🔧 CONFIGURACIÓN VS CODE

### 📦 Extensiones Requeridas
- [ ] **React Native Tools** - Microsoft
- [ ] **TypeScript Hero** - rbbit
- [ ] **Prettier** - Prettier
- [ ] **ES7+ React/Redux/React-Native snippets** - dsznajder
- [ ] **Docker** - Microsoft
- [ ] **GitLens** - GitKraken
- [ ] **Auto Rename Tag** - Jun Han
- [ ] **Bracket Pair Colorizer** - CoenraadS

### ⚙️ settings.json
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "typescriptreact"
  }
}
```

## 🧪 VERIFICACIÓN DE INSTALACIÓN

### ✅ Comandos de Verificación
```bash
# Node.js y npm
node --version          # Debe mostrar v18.x.x
npm --version           # Debe mostrar 9.x.x o superior

# Git
git --version           # Debe mostrar version 2.x.x

# Docker
docker --version        # Debe mostrar version 20.x.x
docker-compose --version # Debe mostrar version 2.x.x

# Expo
expo --version          # Debe mostrar version 49.x.x
eas --version           # Debe mostrar version 5.x.x

# React Native (después de crear proyecto)
npx react-native --version
```

### 🔍 Test de Docker
```bash
# Probar Docker
docker run hello-world

# Probar PostgreSQL
docker run --rm -e POSTGRES_PASSWORD=test postgres:15 postgres --version

# Probar Redis
docker run --rm redis:7-alpine redis-server --version
```

## 📋 CHECKLIST FINAL

### ✅ Antes de Empezar Desarrollo
- [ ] Node.js v18+ instalado y funcionando
- [ ] Git configurado con tu nombre y email
- [ ] VS Code con todas las extensiones
- [ ] Docker Desktop corriendo
- [ ] PostgreSQL y Redis funcionando via Docker
- [ ] Expo CLI instalado globalmente
- [ ] Android Studio con emulador configurado
- [ ] Expo Go instalado en tu teléfono
- [ ] Postman/Insomnia para testing APIs
- [ ] Cuenta en GitHub para repositorios

### 🚀 Test Final
```bash
# Crear proyecto de prueba
npx create-expo-app TestApp --template
cd TestApp
npm start

# Debe abrir Expo DevTools
# Debe mostrar QR code
# Debe funcionar en Expo Go
```

## 🆘 SOLUCIÓN DE PROBLEMAS COMUNES

### 🐳 Docker Issues
```bash
# Si Docker no inicia en Windows:
# 1. Habilitar Hyper-V
# 2. Habilitar WSL2
# 3. Reiniciar PC

# Si hay problemas de permisos en Linux:
sudo usermod -aG docker $USER
# Reiniciar sesión
```

### 📱 React Native Issues
```bash
# Limpiar cache de Expo
expo r -c

# Limpiar cache de npm
npm start -- --reset-cache

# Reinstalar node_modules
rm -rf node_modules
npm install
```

### 🗄️ Database Issues
```bash
# Resetear base de datos
docker-compose down -v
docker-compose up -d

# Ver logs de PostgreSQL
docker-compose logs db
```

---

**OBJETIVO:** Tener todo el entorno listo para desarrollar TaskApp sin problemas
**TIEMPO ESTIMADO:** 2-4 horas dependiendo de tu conexión a internet
