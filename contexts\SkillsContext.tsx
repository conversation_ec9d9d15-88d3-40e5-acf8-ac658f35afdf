import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export interface Skill {
  id: string
  name: string
  category: string
  hourlyRate?: number
  isFlexibleRate: boolean
  experience: "beginner" | "intermediate" | "expert"
  description?: string
  isActive: boolean
  completedTasks: number
  rating: number
  reviews: number
}

export interface SkillCategory {
  id: string
  name: string
  icon: string
  color: string
  skills: string[]
}

export interface JobRecommendation {
  id: string
  title: string
  category: string
  description: string
  hourlyRate: number
  location: string
  distance: number
  matchScore: number
  requiredSkills: string[]
  estimatedDuration: string
  clientRating: number
  urgency: "low" | "medium" | "high"
}

interface SkillsContextType {
  skills: Skill[]
  categories: SkillCategory[]
  recommendations: JobRecommendation[]
  addSkill: (skill: Omit<Skill, "id" | "completedTasks" | "rating" | "reviews">) => void
  updateSkill: (id: string, updates: Partial<Skill>) => void
  removeSkill: (id: string) => void
  getRecommendations: () => Promise<JobRecommendation[]>
  refreshRecommendations: () => Promise<void>
  isLoading: boolean
}

const SkillsContext = createContext<SkillsContextType | undefined>(undefined)

const defaultCategories: SkillCategory[] = [
  {
    id: "assembly",
    name: "Ensamblaje",
    icon: "🔧",
    color: "#3B82F6",
    skills: ["Ensamblaje de Muebles", "Ensamblaje IKEA", "Instalación de Estantes"]
  },
  {
    id: "mounting",
    name: "Montaje",
    icon: "📺",
    color: "#10B981",
    skills: ["Montaje de TV", "Montaje General", "Instalación en Pared"]
  },
  {
    id: "personal_assistance",
    name: "Asistencia Personal",
    icon: "🤝",
    color: "#F59E0B",
    skills: ["Hacer Filas", "Compras", "Organización"]
  },
  {
    id: "cleaning",
    name: "Limpieza",
    icon: "🧹",
    color: "#8B5CF6",
    skills: ["Limpieza Residencial", "Limpieza de Oficinas", "Limpieza Profunda"]
  },
  {
    id: "delivery",
    name: "Entrega",
    icon: "📦",
    color: "#EF4444",
    skills: ["Entrega de Paquetes", "Mudanzas Pequeñas", "Transporte"]
  },
  {
    id: "handyman",
    name: "Reparaciones",
    icon: "🔨",
    color: "#6B7280",
    skills: ["Reparaciones Menores", "Plomería Básica", "Electricidad Básica"]
  }
]

export function SkillsProvider({ children }: { children: React.ReactNode }) {
  const [skills, setSkills] = useState<Skill[]>([])
  const [recommendations, setRecommendations] = useState<JobRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    loadSkills()
  }, [])

  const loadSkills = async () => {
    try {
      const savedSkills = await AsyncStorage.getItem("userSkills")
      if (savedSkills) {
        setSkills(JSON.parse(savedSkills))
      }
    } catch (error) {
      console.error("Error loading skills:", error)
    }
  }

  const saveSkills = async (newSkills: Skill[]) => {
    try {
      await AsyncStorage.setItem("userSkills", JSON.stringify(newSkills))
      setSkills(newSkills)
    } catch (error) {
      console.error("Error saving skills:", error)
    }
  }

  const addSkill = (skillData: Omit<Skill, "id" | "completedTasks" | "rating" | "reviews">) => {
    const newSkill: Skill = {
      ...skillData,
      id: Date.now().toString(),
      completedTasks: 0,
      rating: 0,
      reviews: 0
    }
    const updatedSkills = [...skills, newSkill]
    saveSkills(updatedSkills)
  }

  const updateSkill = (id: string, updates: Partial<Skill>) => {
    const updatedSkills = skills.map(skill =>
      skill.id === id ? { ...skill, ...updates } : skill
    )
    saveSkills(updatedSkills)
  }

  const removeSkill = (id: string) => {
    const updatedSkills = skills.filter(skill => skill.id !== id)
    saveSkills(updatedSkills)
  }

  const calculateMatchScore = (job: any, userSkills: Skill[]): number => {
    let score = 0
    const activeSkills = userSkills.filter(skill => skill.isActive)
    
    // Coincidencia de habilidades (40%)
    const matchingSkills = job.requiredSkills.filter((reqSkill: string) =>
      activeSkills.some(userSkill => userSkill.name.toLowerCase().includes(reqSkill.toLowerCase()))
    )
    score += (matchingSkills.length / job.requiredSkills.length) * 40

    // Experiencia del usuario (30%)
    const relevantSkills = activeSkills.filter(skill =>
      job.requiredSkills.some((reqSkill: string) =>
        skill.name.toLowerCase().includes(reqSkill.toLowerCase())
      )
    )
    if (relevantSkills.length > 0) {
      const avgExperience = relevantSkills.reduce((sum, skill) => {
        const expScore = skill.experience === "expert" ? 3 : skill.experience === "intermediate" ? 2 : 1
        return sum + expScore
      }, 0) / relevantSkills.length
      score += (avgExperience / 3) * 30
    }

    // Tarifa competitiva (20%)
    const userRates = relevantSkills.map(skill => skill.hourlyRate || 0).filter(rate => rate > 0)
    if (userRates.length > 0) {
      const avgUserRate = userRates.reduce((sum, rate) => sum + rate, 0) / userRates.length
      const rateDiff = Math.abs(job.hourlyRate - avgUserRate) / job.hourlyRate
      score += Math.max(0, (1 - rateDiff)) * 20
    }

    // Distancia (10%)
    const distanceScore = Math.max(0, (10 - job.distance) / 10)
    score += distanceScore * 10

    return Math.min(100, Math.max(0, score))
  }

  const getRecommendations = async (): Promise<JobRecommendation[]> => {
    // Simulación de trabajos disponibles
    const mockJobs = [
      {
        id: "1",
        title: "Ensamblaje de Muebles IKEA",
        category: "assembly",
        description: "Ensamblar una cómoda y dos mesitas de noche IKEA",
        hourlyRate: 45,
        location: "Centro, Ciudad",
        distance: 2.5,
        requiredSkills: ["Ensamblaje IKEA", "Ensamblaje de Muebles"],
        estimatedDuration: "2-3 horas",
        clientRating: 4.8,
        urgency: "medium" as const
      },
      {
        id: "2",
        title: "Montaje de TV en Pared",
        category: "mounting",
        description: "Montar TV de 55 pulgadas en pared de concreto",
        hourlyRate: 50,
        location: "Norte, Ciudad",
        distance: 5.2,
        requiredSkills: ["Montaje de TV", "Instalación en Pared"],
        estimatedDuration: "1-2 horas",
        clientRating: 4.9,
        urgency: "high" as const
      },
      {
        id: "3",
        title: "Hacer Fila en Banco",
        category: "personal_assistance",
        description: "Hacer fila para trámite bancario por la mañana",
        hourlyRate: 25,
        location: "Sur, Ciudad",
        distance: 3.8,
        requiredSkills: ["Hacer Filas", "Asistencia Personal"],
        estimatedDuration: "2-4 horas",
        clientRating: 4.5,
        urgency: "low" as const
      }
    ]

    const recommendationsWithScore = mockJobs.map(job => ({
      ...job,
      matchScore: calculateMatchScore(job, skills)
    })).sort((a, b) => b.matchScore - a.matchScore)

    return recommendationsWithScore
  }

  const refreshRecommendations = async () => {
    setIsLoading(true)
    try {
      const newRecommendations = await getRecommendations()
      setRecommendations(newRecommendations)
    } catch (error) {
      console.error("Error refreshing recommendations:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const value: SkillsContextType = {
    skills,
    categories: defaultCategories,
    recommendations,
    addSkill,
    updateSkill,
    removeSkill,
    getRecommendations,
    refreshRecommendations,
    isLoading
  }

  return <SkillsContext.Provider value={value}>{children}</SkillsContext.Provider>
}

export function useSkills() {
  const context = useContext(SkillsContext)
  if (!context) {
    throw new Error("useSkills must be used within a SkillsProvider")
  }
  return context
}
