{"name": "taskapp-latam", "version": "1.0.0", "private": true, "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "eject": "expo eject"}, "dependencies": {"@react-native-async-storage/async-storage": "~1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "expo": "~51.0.28", "expo-image-picker": "~15.0.7", "expo-local-authentication": "~14.0.1", "expo-location": "^18.1.6", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-hook-form": "^7.54.1", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "^0.73.0", "typescript": "^5.1.3"}}