import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Modal, FlatList } from "react-native"
import DateTimePicker from "@react-native-community/datetimepicker"
import { Ionicons } from "@expo/vector-icons"
import { useTheme } from "../contexts/ThemeContext"
import { useGoals, type Goal } from "../contexts/GoalsContext"
import { useUser } from "../contexts/UserContext"

interface AITask {
  id: string
  title: string
  description: string
  priority: "high" | "medium" | "low"
  estimatedTime: string
  completed: boolean
}

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
}

export default function GoalsScreen() {
  const { colors } = useTheme()
  const { user } = useUser()
  const { goals, addGoal, updateGoal, deleteGoal, getProgress } = useGoals()
  const [showModal, setShowModal] = useState(false)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [showAIChat, setShowAIChat] = useState(false)
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null)
  const [aiTasks, setAiTasks] = useState<AITask[]>([])
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatInput, setChatInput] = useState("")
  const [newGoal, setNewGoal] = useState({
    title: "",
    targetAmount: "",
    type: "income" as "income" | "tasks" | "spending",
    deadline: new Date(),
  })

  const generateAITasks = (goal: Goal): AITask[] => {
    const tasks: AITask[] = []
    const timeToDeadline = Math.ceil((goal.deadline.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    const remainingAmount = goal.targetAmount - goal.currentAmount

    if (goal.type === "income") {
      const dailyTarget = remainingAmount / Math.max(timeToDeadline, 1)

      tasks.push(
        {
          id: "1",
          title: "Revisar trabajos disponibles",
          description: `Busca trabajos que paguen al menos $${(dailyTarget * 0.5).toFixed(0)} por día`,
          priority: "high",
          estimatedTime: "30 min",
          completed: false
        },
        {
          id: "2",
          title: "Actualizar perfil profesional",
          description: "Mejora tu perfil para atraer más clientes y trabajos mejor pagados",
          priority: "medium",
          estimatedTime: "1 hora",
          completed: false
        },
        {
          id: "3",
          title: "Aplicar a trabajos urgentes",
          description: "Los trabajos urgentes suelen pagar mejor. Revisa la sección de trabajos con alta prioridad",
          priority: "high",
          estimatedTime: "45 min",
          completed: false
        }
      )

      if (remainingAmount > 500) {
        tasks.push({
          id: "4",
          title: "Considerar trabajos de fin de semana",
          description: "Los trabajos de fin de semana pueden ayudarte a alcanzar tu meta más rápido",
          priority: "medium",
          estimatedTime: "Variable",
          completed: false
        })
      }
    } else if (goal.type === "tasks") {
      const tasksPerDay = remainingAmount / Math.max(timeToDeadline, 1)

      tasks.push(
        {
          id: "1",
          title: "Completar tareas rápidas",
          description: `Enfócate en tareas que puedas completar en menos de 2 horas. Necesitas ${tasksPerDay.toFixed(1)} tareas por día`,
          priority: "high",
          estimatedTime: "Variable",
          completed: false
        },
        {
          id: "2",
          title: "Optimizar tu horario",
          description: "Organiza tu tiempo para maximizar el número de tareas que puedes completar",
          priority: "medium",
          estimatedTime: "20 min",
          completed: false
        }
      )
    }

    return tasks
  }

  const handleAddGoal = async () => {
    if (!newGoal.title || !newGoal.targetAmount) {
      Alert.alert("Error", "Por favor completa todos los campos")
      return
    }

    await addGoal({
      title: newGoal.title,
      targetAmount: Number.parseFloat(newGoal.targetAmount),
      currentAmount: 0,
      type: newGoal.type,
      deadline: newGoal.deadline,
    })

    setNewGoal({
      title: "",
      targetAmount: "",
      type: "income",
      deadline: new Date(),
    })
    setShowModal(false)
    Alert.alert("¡Genial!", "Meta creada exitosamente")
  }

  const openAIAssistant = (goal: Goal) => {
    setSelectedGoal(goal)
    const tasks = generateAITasks(goal)
    setAiTasks(tasks)

    // Mensaje inicial del asistente
    const welcomeMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `¡Hola! Soy tu asistente de IA. He analizado tu meta "${goal.title}" y he generado algunas tareas específicas para ayudarte a alcanzarla. ¿Te gustaría que te explique alguna de estas tareas o necesitas consejos adicionales?`,
      isUser: false,
      timestamp: new Date()
    }

    setChatMessages([welcomeMessage])
    setShowAIChat(true)
  }

  const sendChatMessage = () => {
    if (!chatInput.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: chatInput,
      isUser: true,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, userMessage])

    // Simular respuesta de IA
    setTimeout(() => {
      const aiResponse = generateAIResponse(chatInput, selectedGoal)
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, aiMessage])
    }, 1000)

    setChatInput("")
  }

  const generateAIResponse = (userInput: string, goal: Goal | null): string => {
    const input = userInput.toLowerCase()

    if (input.includes("tiempo") || input.includes("cuánto")) {
      return "Basándome en tu meta y el tiempo restante, te recomiendo dedicar al menos 2-3 horas diarias a trabajar hacia tu objetivo. ¿Te gustaría que te ayude a crear un horario específico?"
    }

    if (input.includes("dinero") || input.includes("ganar")) {
      return "Para maximizar tus ingresos, te sugiero enfocarte en trabajos de montaje y ensamblaje, ya que suelen pagar mejor. También considera trabajos urgentes que ofrecen bonificaciones."
    }

    if (input.includes("tarea") || input.includes("trabajo")) {
      return "Te recomiendo priorizar trabajos que coincidan con tus habilidades principales. Los trabajos de ensamblaje de muebles y montaje de TV suelen ser bien pagados y están en alta demanda."
    }

    if (input.includes("ayuda") || input.includes("consejo")) {
      return "¡Por supuesto! Aquí tienes algunos consejos: 1) Mantén tu perfil actualizado, 2) Responde rápidamente a las ofertas, 3) Construye una buena reputación completando trabajos de calidad. ¿Hay algo específico en lo que te gustaría que te ayude?"
    }

    return "Entiendo tu pregunta. Te sugiero revisar las tareas que he generado para ti. Están diseñadas específicamente para ayudarte a alcanzar tu meta de manera eficiente. ¿Te gustaría que modifique alguna de ellas?"
  }

  const handleUpdateProgress = (goal: Goal) => {
    Alert.prompt(
      "Actualizar Progreso",
      `Progreso actual: $${goal.currentAmount}`,
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Actualizar",
          onPress: (value) => {
            if (value) {
              const newAmount = Number.parseFloat(value)
              if (!isNaN(newAmount)) {
                updateGoal(goal.id, { currentAmount: newAmount })
              }
            }
          },
        },
      ],
      "plain-text",
      goal.currentAmount.toString(),
    )
  }

  const handleCreateGoal = (type: string, title: string, targetAmount: number) => {
    const newGoal: Goal = {
      id: Date.now().toString(),
      title,
      type,
      targetAmount,
      currentAmount: 0,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 días desde hoy
      createdAt: new Date(),
    }

    addGoal(newGoal)
    Alert.alert('¡Meta creada!', `Tu meta "${title}" ha sido creada exitosamente.`)
  }

  const getGoalTypeIcon = (type: string) => {
    switch (type) {
      case "income":
        return "💰"
      case "tasks":
        return "✅"
      case "spending":
        return "🛒"
      default:
        return "🎯"
    }
  }

  const getGoalTypeLabel = (type: string) => {
    switch (type) {
      case "income":
        return "Ingresos"
      case "tasks":
        return "Tareas"
      case "spending":
        return "Gastos"
      default:
        return "Meta"
    }
  }

  const getDaysRemaining = (deadline: Date) => {
    const today = new Date()
    const diffTime = deadline.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Mis Ingresos</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Gestiona tu dinero y establece metas de ingresos mensuales
          </Text>
        </View>



        {/* Quick Stats */}
        <View style={[styles.statsContainer, { backgroundColor: colors.surface }]}>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#10B981' }]}>
              ${user?.currentRole === 'worker' ? '2,450,000' : '850,000'}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {user?.currentRole === 'worker' ? 'Ganado este mes' : 'Ahorrado este mes'}
            </Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>
              {user?.currentRole === 'worker' ? '23' : '8'}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {user?.currentRole === 'worker' ? 'Trabajos completados' : 'Servicios contratados'}
            </Text>
          </View>
        </View>

        {/* Metas Rápidas */}
        <View style={[styles.suggestionsContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>💰 Crear Meta Rápida</Text>

          <View style={styles.suggestionsList}>
            <TouchableOpacity
              style={[styles.suggestionCard, { backgroundColor: colors.primaryLight }]}
              onPress={() => handleCreateGoal('income', 'Ingresos de este mes', user?.currentRole === 'worker' ? 2500000 : 1000000)}
            >
              <Text style={styles.suggestionIcon}>📈</Text>
              <View style={styles.suggestionContent}>
                <Text style={[styles.suggestionTitle, { color: colors.primary }]}>
                  Meta de ingresos mensual
                </Text>
                <Text style={[styles.suggestionDesc, { color: colors.text }]}>
                  ${user?.currentRole === 'worker' ? '2,500,000' : '1,000,000'} para este mes
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.suggestionCard, { backgroundColor: colors.backgroundGray }]}
              onPress={() => handleCreateGoal('income', 'Ahorro mensual', 500000)}
            >
              <Text style={styles.suggestionIcon}>🏦</Text>
              <View style={styles.suggestionContent}>
                <Text style={[styles.suggestionTitle, { color: colors.text }]}>
                  Meta de ahorro
                </Text>
                <Text style={[styles.suggestionDesc, { color: colors.textSecondary }]}>
                  Ahorrar $500,000 este mes
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.suggestionCard, { backgroundColor: colors.backgroundGray }]}
              onPress={() => handleCreateGoal('income', 'Fondo de emergencia', 3000000)}
            >
              <Text style={styles.suggestionIcon}>🛡️</Text>
              <View style={styles.suggestionContent}>
                <Text style={[styles.suggestionTitle, { color: colors.text }]}>
                  Fondo de emergencia
                </Text>
                <Text style={[styles.suggestionDesc, { color: colors.textSecondary }]}>
                  Crear un fondo de $3,000,000
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {goals.length === 0 ? (
          <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
            <Text style={styles.emptyEmoji}>💰</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>¡Empieza a gestionar tu dinero!</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              Crea tu primera meta de ingresos o ahorro usando las opciones de arriba
            </Text>
          </View>
        ) : (
          <View style={styles.goalsContainer}>
            {goals.map((goal) => {
              const progress = getProgress(goal.id)
              const daysRemaining = getDaysRemaining(goal.deadline)

              return (
                <View
                  key={goal.id}
                  style={[styles.goalCard, { backgroundColor: colors.surface }]}
                >
                  <View style={styles.goalHeader}>
                    <View style={styles.goalTitleContainer}>
                      <Text style={styles.goalIcon}>{getGoalTypeIcon(goal.type)}</Text>
                      <View>
                        <Text style={[styles.goalTitle, { color: colors.text }]}>{goal.title}</Text>
                        <Text style={[styles.goalType, { color: colors.textSecondary }]}>
                          {getGoalTypeLabel(goal.type)}
                        </Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={() => handleUpdateProgress(goal)}
                      style={[styles.updateButton, { backgroundColor: colors.primary }]}
                    >
                      <Text style={styles.updateButtonText}>Actualizar</Text>
                    </TouchableOpacity>
                  </View>

                  <View style={styles.progressSection}>
                    <View style={styles.progressInfo}>
                      <Text style={[styles.progressText, { color: '#10B981' }]}>
                        ${goal.currentAmount} / ${goal.targetAmount}
                      </Text>
                      <Text style={[styles.progressPercentage, { color: colors.primary }]}>{progress.toFixed(0)}%</Text>
                    </View>

                    <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            backgroundColor: progress >= 100 ? colors.success : colors.primary,
                            width: `${Math.min(progress, 100)}%`,
                          },
                        ]}
                      />
                    </View>
                  </View>

                  <View style={styles.goalFooter}>
                    <Text style={[styles.deadline, { color: colors.textSecondary }]}>
                      {daysRemaining > 0
                        ? `${daysRemaining} días restantes`
                        : daysRemaining === 0
                          ? "Vence hoy"
                          : `Venció hace ${Math.abs(daysRemaining)} días`}
                    </Text>

                    <View style={styles.goalActions}>
                      <TouchableOpacity
                        onPress={() => openAIAssistant(goal)}
                        style={[styles.aiButton, { backgroundColor: colors.primary }]}
                      >
                        <Text style={styles.aiButtonText}>🤖 IA</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => {
                          Alert.alert("Eliminar Meta", "¿Estás seguro que quieres eliminar esta meta?", [
                            { text: "Cancelar", style: "cancel" },
                            { text: "Eliminar", onPress: () => deleteGoal(goal.id), style: "destructive" },
                          ])
                        }}
                        style={styles.deleteButton}
                      >
                        <Text style={[styles.deleteButtonText, { color: colors.error }]}>Eliminar</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              )
            })}
          </View>
        )}
      </ScrollView>

      <TouchableOpacity style={[styles.fab, { backgroundColor: colors.primary }]} onPress={() => setShowModal(true)}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>

      <Modal visible={showModal} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={[styles.modalCancel, { color: colors.primary }]}>Cancelar</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Nueva Meta</Text>
            <TouchableOpacity onPress={handleAddGoal}>
              <Text style={[styles.modalSave, { color: colors.primary }]}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Título de la meta</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="Ej: Ganar $500 este mes"
                placeholderTextColor={colors.textSecondary}
                value={newGoal.title}
                onChangeText={(text) => setNewGoal({ ...newGoal, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Tipo de meta</Text>
              <View style={styles.typeButtons}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    {
                      backgroundColor: colors.primary,
                      borderColor: colors.primary,
                    },
                  ]}
                  onPress={() => setNewGoal({ ...newGoal, type: 'income' })}
                >
                  <Text style={styles.typeEmoji}>💰</Text>
                  <Text style={[styles.typeText, { color: "#FFFFFF" }]}>
                    Dinero
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>¿Cuánto dinero quieres alcanzar?</Text>
              <View style={[styles.moneyInputContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Text style={[styles.currencySymbol, { color: colors.text }]}>$</Text>
                <TextInput
                  style={[styles.moneyInput, { color: colors.text }]}
                  placeholder="1,000,000"
                  placeholderTextColor={colors.textSecondary}
                  value={newGoal.targetAmount}
                  onChangeText={(text) => setNewGoal({ ...newGoal, targetAmount: text })}
                  keyboardType="numeric"
                />
                <Text style={[styles.currencyLabel, { color: colors.textSecondary }]}>COP</Text>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Fecha límite</Text>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={[styles.dateText, { color: colors.text }]}>{newGoal.deadline.toLocaleDateString()}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          {showDatePicker && (
            <DateTimePicker
              value={newGoal.deadline}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false)
                if (selectedDate) {
                  setNewGoal({ ...newGoal, deadline: selectedDate })
                }
              }}
            />
          )}
        </View>
      </Modal>

      {/* Modal del Asistente IA */}
      <Modal visible={showAIChat} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAIChat(false)}>
              <Text style={[styles.modalCancel, { color: colors.primary }]}>Cerrar</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Asistente IA</Text>
            <View style={{ width: 60 }} />
          </View>

          <View style={styles.aiContainer}>
            {/* Tareas generadas por IA */}
            <View style={styles.aiTasksSection}>
              <Text style={[styles.aiSectionTitle, { color: colors.text }]}>
                Tareas Recomendadas para "{selectedGoal?.title}"
              </Text>

              <ScrollView style={styles.aiTasksList} showsVerticalScrollIndicator={false}>
                {aiTasks.map((task) => (
                  <View key={task.id} style={[styles.aiTaskCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                    <View style={styles.aiTaskHeader}>
                      <Text style={[styles.aiTaskTitle, { color: colors.text }]}>{task.title}</Text>
                      <View style={[
                        styles.priorityBadge,
                        { backgroundColor: task.priority === "high" ? colors.error : task.priority === "medium" ? colors.warning : colors.success }
                      ]}>
                        <Text style={styles.priorityText}>
                          {task.priority === "high" ? "Alta" : task.priority === "medium" ? "Media" : "Baja"}
                        </Text>
                      </View>
                    </View>

                    <Text style={[styles.aiTaskDescription, { color: colors.textSecondary }]}>
                      {task.description}
                    </Text>

                    <View style={styles.aiTaskFooter}>
                      <Text style={[styles.aiTaskTime, { color: colors.textSecondary }]}>
                        ⏱️ {task.estimatedTime}
                      </Text>

                      <TouchableOpacity
                        style={[
                          styles.taskCompleteButton,
                          { backgroundColor: task.completed ? colors.success : colors.border }
                        ]}
                        onPress={() => {
                          const updatedTasks = aiTasks.map(t =>
                            t.id === task.id ? { ...t, completed: !t.completed } : t
                          )
                          setAiTasks(updatedTasks)
                        }}
                      >
                        <Text style={[
                          styles.taskCompleteText,
                          { color: task.completed ? "white" : colors.textSecondary }
                        ]}>
                          {task.completed ? "✓ Completada" : "Marcar como completada"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </ScrollView>
            </View>

            {/* Chat con IA */}
            <View style={styles.aiChatSection}>
              <Text style={[styles.aiSectionTitle, { color: colors.text }]}>
                Chat con Asistente
              </Text>

              <ScrollView style={styles.chatMessages} showsVerticalScrollIndicator={false}>
                {chatMessages.map((message) => (
                  <View
                    key={message.id}
                    style={[
                      styles.chatMessage,
                      message.isUser ? styles.userMessage : styles.aiMessage,
                      { backgroundColor: message.isUser ? colors.primary : colors.surface }
                    ]}
                  >
                    <Text style={[
                      styles.chatMessageText,
                      { color: message.isUser ? "white" : colors.text }
                    ]}>
                      {message.text}
                    </Text>
                  </View>
                ))}
              </ScrollView>

              <View style={[styles.chatInput, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <TextInput
                  style={[styles.chatTextInput, { color: colors.text }]}
                  placeholder="Pregúntame sobre tu meta..."
                  placeholderTextColor={colors.textSecondary}
                  value={chatInput}
                  onChangeText={setChatInput}
                  multiline
                />
                <TouchableOpacity
                  style={[styles.sendButton, { backgroundColor: colors.primary }]}
                  onPress={sendChatMessage}
                >
                  <Text style={styles.sendButtonText}>Enviar</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    gap: 20,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  suggestionsContainer: {
    margin: 20,
    borderRadius: 16,
    padding: 20,
  },
  suggestionsList: {
    gap: 12,
  },
  suggestionCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  suggestionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  suggestionDesc: {
    fontSize: 14,
    lineHeight: 18,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
  },
  emptyState: {
    margin: 20,
    padding: 40,
    borderRadius: 15,
    alignItems: "center",
  },
  emptyEmoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 22,
  },
  goalsContainer: {
    paddingHorizontal: 0,
  },
  goalCard: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    marginBottom: 0,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  goalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  goalTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  goalIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  goalTitle: {
    fontSize: 17,
    fontWeight: "500",
    marginBottom: 2,
  },
  goalType: {
    fontSize: 14,
  },
  updateButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  updateButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  progressSection: {
    marginBottom: 15,
  },
  progressInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: "bold",
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  goalFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  goalActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  deadline: {
    fontSize: 14,
  },
  aiButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 10,
  },
  aiButtonText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  deleteButton: {
    padding: 5,
  },
  deleteButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  fab: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  fabText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
  },
  modalCancel: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  modalSave: {
    fontSize: 16,
    fontWeight: "600",
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
  },
  moneyInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
  moneyInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 15,
  },
  currencyLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  typeButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  typeButton: {
    flex: 1,
    alignItems: "center",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    marginHorizontal: 5,
  },
  typeEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  typeText: {
    fontSize: 14,
    fontWeight: "600",
  },
  dateButton: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
  },
  dateText: {
    fontSize: 16,
  },
  // Estilos para IA
  aiContainer: {
    flex: 1,
    padding: 20,
  },
  aiSectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  aiTasksSection: {
    flex: 1,
    marginBottom: 20,
  },
  aiTasksList: {
    flex: 1,
  },
  aiTaskCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  aiTaskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  aiTaskTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: "600",
    color: "white",
  },
  aiTaskDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  aiTaskFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  aiTaskTime: {
    fontSize: 12,
  },
  taskCompleteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  taskCompleteText: {
    fontSize: 12,
    fontWeight: "600",
  },
  aiChatSection: {
    height: 300,
  },
  chatMessages: {
    flex: 1,
    marginBottom: 10,
  },
  chatMessage: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    maxWidth: "80%",
  },
  userMessage: {
    alignSelf: "flex-end",
  },
  aiMessage: {
    alignSelf: "flex-start",
  },
  chatMessageText: {
    fontSize: 14,
    lineHeight: 18,
  },
  chatInput: {
    flexDirection: "row",
    alignItems: "flex-end",
    borderWidth: 1,
    borderRadius: 12,
    padding: 8,
  },
  chatTextInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingHorizontal: 8,
  },
  sendButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  infoCard: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 8,
  },
  sendButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
})
