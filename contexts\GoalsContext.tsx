import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export interface Goal {
  id: string
  title: string
  targetAmount: number
  currentAmount: number
  deadline: Date
  type: "income" | "tasks" | "spending"
}

interface GoalsContextType {
  goals: Goal[]
  addGoal: (goal: Omit<Goal, "id">) => Promise<void>
  updateGoal: (id: string, updates: Partial<Goal>) => Promise<void>
  deleteGoal: (id: string) => Promise<void>
  getProgress: (goalId: string) => number
}

const GoalsContext = createContext<GoalsContextType | undefined>(undefined)

export function GoalsProvider({ children }: { children: React.ReactNode }) {
  const [goals, setGoals] = useState<Goal[]>([])

  useEffect(() => {
    loadGoals()
  }, [])

  const loadGoals = async () => {
    try {
      const goalsData = await AsyncStorage.getItem("goals")
      if (goalsData) {
        const parsedGoals = JSON.parse(goalsData).map((goal: any) => ({
          ...goal,
          deadline: new Date(goal.deadline),
        }))
        setGoals(parsedGoals)
      }
    } catch (error) {
      console.error("Error cargando metas:", error)
    }
  }

  const saveGoals = async (newGoals: Goal[]) => {
    try {
      await AsyncStorage.setItem("goals", JSON.stringify(newGoals))
    } catch (error) {
      console.error("Error guardando metas:", error)
    }
  }

  const addGoal = async (goalData: Omit<Goal, "id">) => {
    const newGoal: Goal = {
      ...goalData,
      id: Date.now().toString(),
    }
    const updatedGoals = [...goals, newGoal]
    setGoals(updatedGoals)
    await saveGoals(updatedGoals)
  }

  const updateGoal = async (id: string, updates: Partial<Goal>) => {
    const updatedGoals = goals.map((goal) => (goal.id === id ? { ...goal, ...updates } : goal))
    setGoals(updatedGoals)
    await saveGoals(updatedGoals)
  }

  const deleteGoal = async (id: string) => {
    const updatedGoals = goals.filter((goal) => goal.id !== id)
    setGoals(updatedGoals)
    await saveGoals(updatedGoals)
  }

  const getProgress = (goalId: string): number => {
    const goal = goals.find((g) => g.id === goalId)
    if (!goal) return 0
    return Math.min((goal.currentAmount / goal.targetAmount) * 100, 100)
  }

  return (
    <GoalsContext.Provider
      value={{
        goals,
        addGoal,
        updateGoal,
        deleteGoal,
        getProgress,
      }}
    >
      {children}
    </GoalsContext.Provider>
  )
}

export function useGoals() {
  const context = useContext(GoalsContext)
  if (!context) {
    throw new Error("useGoals debe ser usado dentro de un GoalsProvider")
  }
  return context
}
