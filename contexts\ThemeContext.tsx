import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

type Theme = "light" | "dark"

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  colors: {
    primary: string
    primaryLight: string
    primaryDark: string
    background: string
    backgroundGray: string
    surface: string
    surfaceGray: string
    text: string
    textSecondary: string
    textLight: string
    border: string
    borderLight: string
    success: string
    warning: string
    error: string
    info: string
    notification: string
  }
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

const lightColors = {
  primary: "#1DA1F2",        // Azul X (Twitter) moderno y profesional
  primaryLight: "#E8F5FE",   // Azul muy claro para backgrounds
  primaryDark: "#1976D2",    // Azul oscuro para acentos
  background: "#FFFFFF",     // Blanco puro
  backgroundGray: "#F8F9FA", // Gris muy claro para secciones
  surface: "#FFFFFF",        // Blanco para cards
  surfaceGray: "#F1F3F4",    // Gris claro para cards secundarias
  text: "#212529",           // Casi negro para mejor legibilidad
  textSecondary: "#6C757D",  // Gris medio para texto secundario
  textLight: "#ADB5BD",      // Gris claro para placeholders
  border: "#DEE2E6",         // Borde muy sutil
  borderLight: "#F1F3F4",    // Borde ultra sutil
  success: "#10B981",        // Verde profesional
  warning: "#F59E0B",        // Amarillo profesional
  error: "#EF4444",          // Rojo profesional
  info: "#3B82F6",           // Azul información
  notification: "#EF4444",   // Rojo para notificaciones
}

const darkColors = {
  primary: "#1DA1F2",        // Azul X (Twitter) consistente en modo oscuro
  primaryLight: "#0D47A1",   // Azul oscuro para backgrounds
  primaryDark: "#42A5F5",    // Azul claro para acentos
  background: "#000000",     // Negro puro
  backgroundGray: "#111111", // Gris muy oscuro
  surface: "#1C1C1E",        // Superficie oscura iOS style
  surfaceGray: "#2C2C2E",    // Superficie gris oscura
  text: "#FFFFFF",           // Blanco puro
  textSecondary: "#8E8E93",  // Gris medio iOS style
  textLight: "#636366",      // Gris claro iOS style
  border: "#38383A",         // Borde oscuro iOS style
  borderLight: "#2C2C2E",    // Borde muy oscuro
  success: "#30D158",        // Verde iOS dark
  warning: "#FF9F0A",        // Amarillo iOS dark
  error: "#FF453A",          // Rojo iOS dark
  info: "#64D2FF",           // Azul información
  notification: "#FF453A",   // Rojo para notificaciones
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>("light")

  useEffect(() => {
    loadTheme()
  }, [])

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem("theme")
      if (savedTheme) {
        setTheme(savedTheme as Theme)
      }
    } catch (error) {
      console.error("Error loading theme:", error)
    }
  }

  const toggleTheme = async () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    try {
      await AsyncStorage.setItem("theme", newTheme)
    } catch (error) {
      console.error("Error saving theme:", error)
    }
  }

  const colors = theme === "light" ? lightColors : darkColors

  return <ThemeContext.Provider value={{ theme, toggleTheme, colors }}>{children}</ThemeContext.Provider>
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
