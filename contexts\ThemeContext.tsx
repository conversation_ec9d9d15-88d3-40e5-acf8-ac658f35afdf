import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

type Theme = "light" | "dark"

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  colors: {
    primary: string
    primaryLight: string
    primaryDark: string
    background: string
    backgroundGray: string
    surface: string
    surfaceGray: string
    text: string
    textSecondary: string
    textLight: string
    border: string
    borderLight: string
    success: string
    warning: string
    error: string
    info: string
    notification: string
  }
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

const lightColors = {
  primary: "#4A90E2",        // Azul suave y profesional
  primaryLight: "#E8F2FF",   // Azul muy claro para backgrounds
  primaryDark: "#2E5BBA",    // Azul oscuro para acentos
  background: "#FFFFFF",     // Blanco puro
  backgroundGray: "#FAFAFA", // Gris muy claro para secciones
  surface: "#FFFFFF",        // Blanco para cards
  surfaceGray: "#F5F5F5",    // Gris claro para cards secundarias
  text: "#000000",           // Negro para mejor legibilidad
  textSecondary: "#666666",  // Gris medio para texto secundario
  textLight: "#999999",      // Gris claro para placeholders
  border: "#E0E0E0",         // Borde muy sutil
  borderLight: "#F0F0F0",    // Borde ultra sutil
  success: "#10B981",        // Verde profesional
  warning: "#F59E0B",        // Amarillo profesional
  error: "#EF4444",          // Rojo profesional
  info: "#4A90E2",           // Azul información
  notification: "#EF4444",   // Rojo para notificaciones
}

const darkColors = {
  primary: "#1DA1F2",        // Azul X (Twitter) consistente
  primaryLight: "#1A365D",   // Azul oscuro profesional
  primaryDark: "#63B3ED",    // Azul claro para acentos
  background: "#0F172A",     // Slate 900 - Más profesional que negro puro
  backgroundGray: "#1E293B", // Slate 800 - Gris oscuro elegante
  surface: "#1E293B",        // Slate 800 - Superficie principal
  surfaceGray: "#334155",    // Slate 700 - Superficie secundaria
  text: "#F8FAFC",           // Slate 50 - Blanco suave
  textSecondary: "#CBD5E1",  // Slate 300 - Gris claro legible
  textLight: "#94A3B8",      // Slate 400 - Gris medio
  border: "#475569",         // Slate 600 - Borde visible pero sutil
  borderLight: "#334155",    // Slate 700 - Borde muy sutil
  success: "#10B981",        // Emerald 500 - Verde profesional
  warning: "#F59E0B",        // Amber 500 - Amarillo profesional
  error: "#EF4444",          // Red 500 - Rojo profesional
  info: "#3B82F6",           // Blue 500 - Azul información
  notification: "#EF4444",   // Red 500 - Rojo para notificaciones
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>("light")

  useEffect(() => {
    loadTheme()
  }, [])

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem("theme")
      if (savedTheme) {
        setTheme(savedTheme as Theme)
      }
    } catch (error) {
      console.error("Error loading theme:", error)
    }
  }

  const toggleTheme = async () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    try {
      await AsyncStorage.setItem("theme", newTheme)
    } catch (error) {
      console.error("Error saving theme:", error)
    }
  }

  const colors = theme === "light" ? lightColors : darkColors

  return <ThemeContext.Provider value={{ theme, toggleTheme, colors }}>{children}</ThemeContext.Provider>
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
