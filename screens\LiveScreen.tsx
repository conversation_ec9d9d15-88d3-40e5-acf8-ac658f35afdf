"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, TouchableOpacity, Alert, Dimensions } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

const { width, height } = Dimensions.get("window")

export default function LiveScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { isStreaming, workerId } = route.params || {}
  const [viewers, setViewers] = useState(Math.floor(Math.random() * 50) + 10)
  const [isLive, setIsLive] = useState(isStreaming || false)
  const [streamDuration, setStreamDuration] = useState(0)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isLive) {
      interval = setInterval(() => {
        setStreamDuration((prev) => prev + 1)
        // Simulate viewer changes
        if (Math.random() > 0.7) {
          setViewers((prev) => Math.max(1, prev + (Math.random() > 0.5 ? 1 : -1)))
        }
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isLive])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const startStream = () => {
    Alert.alert("Iniciar Transmisión", "¿Estás listo para comenzar a transmitir tu trabajo en vivo?", [
      { text: "Cancelar", style: "cancel" },
      { text: "Iniciar", onPress: () => setIsLive(true) },
    ])
  }

  const stopStream = () => {
    Alert.alert("Finalizar Transmisión", "¿Quieres terminar la transmisión en vivo?", [
      { text: "Continuar", style: "cancel" },
      {
        text: "Finalizar",
        onPress: () => {
          setIsLive(false)
          navigation.goBack()
        },
      },
    ])
  }

  const mockComments = [
    "¡Excelente trabajo! 👏",
    "¿Cuánto tiempo llevas haciendo esto?",
    "Se ve muy profesional",
    "¿Podrías explicar esa técnica?",
    "Gracias por compartir tu trabajo",
  ]

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Video Area */}
      <View style={[styles.videoContainer, { backgroundColor: "#000000" }]}>
        {isLive ? (
          <View style={styles.liveContent}>
            <Text style={styles.liveText}>📹 TRANSMISIÓN EN VIVO</Text>
            <Text style={styles.simulationText}>
              {user?.role === "worker" ? "Tu cámara estaría activa aquí" : "Viendo el trabajo en tiempo real"}
            </Text>
          </View>
        ) : (
          <View style={styles.offlineContent}>
            <Text style={styles.offlineEmoji}>📱</Text>
            <Text style={styles.offlineText}>Transmisión no iniciada</Text>
            {user?.role === "worker" && (
              <TouchableOpacity style={[styles.startButton, { backgroundColor: colors.error }]} onPress={startStream}>
                <Text style={styles.startButtonText}>🔴 Iniciar Transmisión</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Live Overlay */}
        {isLive && (
          <View style={styles.liveOverlay}>
            <View style={[styles.liveIndicator, { backgroundColor: colors.error }]}>
              <Text style={styles.liveIndicatorText}>🔴 EN VIVO</Text>
            </View>

            <View style={styles.statsContainer}>
              <View style={[styles.statBadge, { backgroundColor: "rgba(0,0,0,0.7)" }]}>
                <Text style={styles.statText}>👥 {viewers}</Text>
              </View>
              <View style={[styles.statBadge, { backgroundColor: "rgba(0,0,0,0.7)" }]}>
                <Text style={styles.statText}>⏱️ {formatDuration(streamDuration)}</Text>
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Controls */}
      {isLive && user?.role === "worker" && (
        <View style={[styles.controlsContainer, { backgroundColor: colors.surface }]}>
          <TouchableOpacity style={[styles.controlButton, { backgroundColor: colors.error }]} onPress={stopStream}>
            <Text style={styles.controlButtonText}>⏹️ Finalizar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.primary }]}
            onPress={() => Alert.alert("Función", "Cambiar cámara (próximamente)")}
          >
            <Text style={styles.controlButtonText}>🔄 Cámara</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Comments Section */}
      {isLive && (
        <View style={[styles.commentsSection, { backgroundColor: colors.surface }]}>
          <Text style={[styles.commentsTitle, { color: colors.text }]}>Comentarios en vivo</Text>
          <View style={styles.commentsList}>
            {mockComments.map((comment, index) => (
              <View key={index} style={styles.commentItem}>
                <Text style={[styles.commentUser, { color: colors.primary }]}>Usuario{index + 1}:</Text>
                <Text style={[styles.commentText, { color: colors.text }]}>{comment}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Info Section */}
      <View style={[styles.infoSection, { backgroundColor: colors.surface }]}>
        <Text style={[styles.infoTitle, { color: colors.text }]}>Transmisión en Vivo</Text>
        <Text style={[styles.infoDescription, { color: colors.textSecondary }]}>
          {user?.role === "worker"
            ? "Comparte tu trabajo en tiempo real para generar confianza con tus clientes"
            : "Observa el trabajo en tiempo real y comunícate directamente con el trabajador"}
        </Text>

        <View style={styles.benefitsList}>
          <Text style={[styles.benefitItem, { color: colors.text }]}>✅ Transparencia total</Text>
          <Text style={[styles.benefitItem, { color: colors.text }]}>✅ Mayor confianza</Text>
          <Text style={[styles.benefitItem, { color: colors.text }]}>✅ Comunicación directa</Text>
          {user?.role === "worker" && (
            <Text style={[styles.benefitItem, { color: colors.text }]}>✅ Más oportunidades de trabajo</Text>
          )}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  videoContainer: {
    height: height * 0.4,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  liveContent: {
    alignItems: "center",
  },
  liveText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 20,
  },
  simulationText: {
    color: "#FFFFFF",
    fontSize: 16,
    textAlign: "center",
    opacity: 0.8,
  },
  offlineContent: {
    alignItems: "center",
  },
  offlineEmoji: {
    fontSize: 60,
    marginBottom: 20,
  },
  offlineText: {
    color: "#FFFFFF",
    fontSize: 18,
    marginBottom: 30,
  },
  startButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  startButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  liveOverlay: {
    position: "absolute",
    top: 20,
    left: 20,
    right: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  liveIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveIndicatorText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  statsContainer: {
    alignItems: "flex-end",
  },
  statBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 5,
  },
  statText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    padding: 20,
  },
  controlButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    minWidth: 120,
    alignItems: "center",
  },
  controlButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  commentsSection: {
    maxHeight: 150,
    padding: 20,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  commentsList: {
    flex: 1,
  },
  commentItem: {
    flexDirection: "row",
    marginBottom: 8,
  },
  commentUser: {
    fontSize: 14,
    fontWeight: "600",
    marginRight: 8,
  },
  commentText: {
    fontSize: 14,
    flex: 1,
  },
  infoSection: {
    flex: 1,
    padding: 20,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  infoDescription: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 20,
  },
  benefitsList: {
    marginTop: 10,
  },
  benefitItem: {
    fontSize: 16,
    marginBottom: 8,
  },
})
