import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  Modal,
  TextInput
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useSkills, Skill, JobRecommendation } from "../contexts/SkillsContext"
import { useNavigation } from "@react-navigation/native"

export default function SkillsScreen() {
  const { colors } = useTheme()
  const navigation = useNavigation()
  const { skills, categories, addSkill, updateSkill, removeSkill, refreshRecommendations, isLoading } = useSkills()
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState("")
  const [newSkillName, setNewSkillName] = useState("")
  const [newSkillRate, setNewSkillRate] = useState("")
  const [newSkillExperience, setNewSkillExperience] = useState<"beginner" | "intermediate" | "expert">("beginner")
  const [refreshing, setRefreshing] = useState(false)

  const onRefresh = async () => {
    setRefreshing(true)
    await refreshRecommendations()
    setRefreshing(false)
  }

  const handleAddSkill = () => {
    if (!newSkillName.trim() || !selectedCategory) {
      Alert.alert("Error", "Por favor completa todos los campos")
      return
    }

    const category = categories.find(cat => cat.id === selectedCategory)
    if (!category) return

    addSkill({
      name: newSkillName.trim(),
      category: selectedCategory,
      hourlyRate: newSkillRate ? parseFloat(newSkillRate) : undefined,
      isFlexibleRate: !newSkillRate,
      experience: newSkillExperience,
      isActive: true
    })

    // Reset form
    setNewSkillName("")
    setNewSkillRate("")
    setNewSkillExperience("beginner")
    setSelectedCategory("")
    setShowAddModal(false)
    
    Alert.alert("¡Éxito!", "Habilidad agregada correctamente")
  }

  const toggleSkillStatus = (skill: Skill) => {
    updateSkill(skill.id, { isActive: !skill.isActive })
  }

  const deleteSkill = (skill: Skill) => {
    Alert.alert(
      "Eliminar Habilidad",
      `¿Estás seguro de que quieres eliminar "${skill.name}"?`,
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Eliminar", style: "destructive", onPress: () => removeSkill(skill.id) }
      ]
    )
  }

  const getSkillsByCategory = (categoryId: string) => {
    return skills.filter(skill => skill.category === categoryId)
  }

  const getExperienceColor = (experience: string) => {
    switch (experience) {
      case "expert": return "#10B981"
      case "intermediate": return "#F59E0B"
      default: return "#6B7280"
    }
  }

  const getExperienceText = (experience: string) => {
    switch (experience) {
      case "expert": return "Experto"
      case "intermediate": return "Intermedio"
      default: return "Principiante"
    }
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 8,
    },
    headerSubtitle: {
      fontSize: 16,
      color: colors.textSecondary,
    },
    recommendationsButton: {
      marginTop: 12,
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderRadius: 8,
      alignItems: "center",
    },
    recommendationsButtonText: {
      color: "white",
      fontSize: 14,
      fontWeight: "600",
    },
    content: {
      flex: 1,
    },
    categorySection: {
      marginBottom: 24,
    },
    categoryHeader: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.surface,
    },
    categoryIcon: {
      fontSize: 24,
      marginRight: 12,
    },
    categoryTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.text,
      flex: 1,
    },
    skillCard: {
      marginHorizontal: 20,
      marginVertical: 8,
      padding: 16,
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    skillHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: 8,
    },
    skillName: {
      fontSize: 16,
      fontWeight: "600",
      color: colors.text,
      flex: 1,
    },
    skillRate: {
      fontSize: 16,
      fontWeight: "bold",
      color: colors.primary,
    },
    skillInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    experienceBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginRight: 8,
    },
    experienceText: {
      fontSize: 12,
      fontWeight: "600",
      color: "white",
    },
    skillStats: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    skillActions: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 12,
    },
    actionButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
    },
    activeButton: {
      backgroundColor: colors.success,
      borderColor: colors.success,
    },
    inactiveButton: {
      backgroundColor: colors.textSecondary,
      borderColor: colors.textSecondary,
    },
    deleteButton: {
      backgroundColor: colors.error,
      borderColor: colors.error,
    },
    actionButtonText: {
      fontSize: 14,
      fontWeight: "600",
      color: "white",
    },
    addButton: {
      position: "absolute",
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: colors.primary,
      justifyContent: "center",
      alignItems: "center",
      elevation: 4,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    addButtonText: {
      fontSize: 24,
      color: "white",
      fontWeight: "bold",
    },
    emptyState: {
      padding: 40,
      alignItems: "center",
    },
    emptyStateText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: "center",
      marginBottom: 16,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0,0,0,0.5)",
      justifyContent: "center",
      alignItems: "center",
    },
    modalContent: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      padding: 24,
      width: "90%",
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: "bold",
      color: colors.text,
      marginBottom: 20,
      textAlign: "center",
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: colors.text,
      marginBottom: 16,
    },
    categorySelector: {
      marginBottom: 16,
    },
    categoryOption: {
      flexDirection: "row",
      alignItems: "center",
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    categoryOptionSelected: {
      backgroundColor: colors.primary + "20",
      borderColor: colors.primary,
    },
    categoryOptionText: {
      fontSize: 16,
      color: colors.text,
      marginLeft: 8,
    },
    experienceSelector: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 20,
    },
    experienceOption: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: "center",
      marginHorizontal: 4,
    },
    experienceOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    experienceOptionText: {
      fontSize: 14,
      color: colors.text,
    },
    experienceOptionTextSelected: {
      color: "white",
    },
    modalButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    modalButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      alignItems: "center",
      marginHorizontal: 8,
    },
    cancelButton: {
      backgroundColor: colors.textSecondary,
    },
    confirmButton: {
      backgroundColor: colors.primary,
    },
    modalButtonText: {
      fontSize: 16,
      fontWeight: "600",
      color: "white",
    },
  })

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mis Habilidades</Text>
        <Text style={styles.headerSubtitle}>
          {skills.filter(s => s.isActive).length} habilidades activas
        </Text>

        {skills.filter(s => s.isActive).length > 0 && (
          <TouchableOpacity
            style={[styles.recommendationsButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.navigate("JobRecommendations" as never)}
          >
            <Text style={styles.recommendationsButtonText}>
              🎯 Ver Trabajos Recomendados
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {categories.map(category => {
          const categorySkills = getSkillsByCategory(category.id)
          
          return (
            <View key={category.id} style={styles.categorySection}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={styles.categoryTitle}>{category.name}</Text>
                <Text style={[styles.skillStats, { color: category.color }]}>
                  {categorySkills.length}
                </Text>
              </View>

              {categorySkills.length > 0 ? (
                categorySkills.map(skill => (
                  <View key={skill.id} style={styles.skillCard}>
                    <View style={styles.skillHeader}>
                      <Text style={styles.skillName}>{skill.name}</Text>
                      <Text style={styles.skillRate}>
                        {skill.hourlyRate ? `$${skill.hourlyRate}/hr` : "Tarifa flexible"}
                      </Text>
                    </View>

                    <View style={styles.skillInfo}>
                      <View style={[
                        styles.experienceBadge,
                        { backgroundColor: getExperienceColor(skill.experience) }
                      ]}>
                        <Text style={styles.experienceText}>
                          {getExperienceText(skill.experience)}
                        </Text>
                      </View>
                      <Text style={styles.skillStats}>
                        {skill.completedTasks} trabajos • ⭐ {skill.rating.toFixed(1)}
                      </Text>
                    </View>

                    <View style={styles.skillActions}>
                      <TouchableOpacity
                        style={[
                          styles.actionButton,
                          skill.isActive ? styles.activeButton : styles.inactiveButton
                        ]}
                        onPress={() => toggleSkillStatus(skill)}
                      >
                        <Text style={styles.actionButtonText}>
                          {skill.isActive ? "Activa" : "Inactiva"}
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.actionButton, styles.deleteButton]}
                        onPress={() => deleteSkill(skill)}
                      >
                        <Text style={styles.actionButtonText}>Eliminar</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>
                    No tienes habilidades en {category.name.toLowerCase()}
                  </Text>
                </View>
              )}
            </View>
          )
        })}
      </ScrollView>

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setShowAddModal(true)}
      >
        <Text style={styles.addButtonText}>+</Text>
      </TouchableOpacity>

      <Modal
        visible={showAddModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Agregar Nueva Habilidad</Text>

            <TextInput
              style={styles.input}
              placeholder="Nombre de la habilidad"
              placeholderTextColor={colors.textSecondary}
              value={newSkillName}
              onChangeText={setNewSkillName}
            />

            <TextInput
              style={styles.input}
              placeholder="Tarifa por hora (opcional)"
              placeholderTextColor={colors.textSecondary}
              value={newSkillRate}
              onChangeText={setNewSkillRate}
              keyboardType="numeric"
            />

            <View style={styles.categorySelector}>
              <Text style={[styles.skillStats, { marginBottom: 8 }]}>Categoría:</Text>
              {categories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryOption,
                    selectedCategory === category.id && styles.categoryOptionSelected
                  ]}
                  onPress={() => setSelectedCategory(category.id)}
                >
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                  <Text style={styles.categoryOptionText}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.experienceSelector}>
              {["beginner", "intermediate", "expert"].map(exp => (
                <TouchableOpacity
                  key={exp}
                  style={[
                    styles.experienceOption,
                    newSkillExperience === exp && styles.experienceOptionSelected
                  ]}
                  onPress={() => setNewSkillExperience(exp as any)}
                >
                  <Text style={[
                    styles.experienceOptionText,
                    newSkillExperience === exp && styles.experienceOptionTextSelected
                  ]}>
                    {getExperienceText(exp)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancelar</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleAddSkill}
              >
                <Text style={styles.modalButtonText}>Agregar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}
