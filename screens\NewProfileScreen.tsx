import React, { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Modal,
  FlatList,
  Dimensions,
} from "react-native"
import * as ImagePicker from "expo-image-picker"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useSkills } from "../contexts/SkillsContext"
import { useNotifications } from "../contexts/NotificationContext"

const { width } = Dimensions.get("window")

interface WorkSample {
  id: string
  title: string
  image: string
  description: string
  rating: number
  date: string
}

interface Review {
  id: string
  clientName: string
  rating: number
  comment: string
  date: string
  taskTitle: string
}

export default function NewProfileScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user, updateProfile, logout } = useUser()
  const { skills } = useSkills()
  const { notifications, unreadCount, markAllAsRead } = useNotifications()
  
  const [showNotifications, setShowNotifications] = useState(false)
  const [expandedSection, setExpandedSection] = useState<string | null>(null)
  const [showWorkSamples, setShowWorkSamples] = useState(false)

  // Datos de ejemplo
  const workSamples: WorkSample[] = [
    {
      id: "1",
      title: "Limpieza de apartamento",
      image: "https://via.placeholder.com/300x200",
      description: "Limpieza profunda de apartamento de 3 habitaciones",
      rating: 5,
      date: "2024-01-15"
    },
    {
      id: "2", 
      title: "Instalación de TV",
      image: "https://via.placeholder.com/300x200",
      description: "Montaje de TV 55 pulgadas en pared",
      rating: 4.8,
      date: "2024-01-10"
    }
  ]

  const reviews: Review[] = [
    {
      id: "1",
      clientName: "María González",
      rating: 5,
      comment: "Excelente trabajo, muy profesional y puntual. Lo recomiendo 100%",
      date: "2024-01-15",
      taskTitle: "Limpieza de apartamento"
    },
    {
      id: "2",
      clientName: "Carlos Rodríguez", 
      rating: 4.8,
      comment: "Muy buen servicio, quedó perfecto el montaje de la TV",
      date: "2024-01-10",
      taskTitle: "Instalación de TV"
    }
  ]

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    })

    if (!result.canceled) {
      updateProfile({ avatar: result.assets[0].uri })
    }
  }

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Text key={i} style={{ color: i < rating ? "#FFD700" : colors.borderLight }}>
        ⭐
      </Text>
    ))
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.backgroundGray }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header con foto de perfil */}
        <View style={[styles.header, { backgroundColor: colors.background }]}>
          <View style={styles.headerTop}>
            <TouchableOpacity
              style={[styles.notificationButton, { backgroundColor: colors.surfaceGray }]}
              onPress={() => setShowNotifications(true)}
            >
              <Text style={styles.notificationIcon}>🔔</Text>
              {unreadCount > 0 && (
                <View style={[styles.notificationBadge, { backgroundColor: colors.notification }]}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadCount > 99 ? "99+" : unreadCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          <TouchableOpacity onPress={pickImage} style={styles.avatarContainer}>
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primaryLight }]}>
                <Text style={[styles.avatarText, { color: colors.primary }]}>
                  {user?.name?.charAt(0).toUpperCase() || "U"}
                </Text>
              </View>
            )}
            <View style={[styles.editBadge, { backgroundColor: colors.primary }]}>
              <Text style={styles.editBadgeText}>📷</Text>
            </View>
          </TouchableOpacity>

          <Text style={[styles.headerName, { color: colors.text }]}>{user?.name || "Usuario"}</Text>
          <Text style={[styles.headerRole, { color: colors.textSecondary }]}>
            {user?.role === "client" ? "👨‍💼 Cliente" : "🧑‍🔧 Trabajador Profesional"}
          </Text>

          {user?.rating && (
            <View style={styles.ratingContainer}>
              <View style={styles.starsContainer}>
                {renderStars(Math.floor(user.rating))}
              </View>
              <Text style={[styles.ratingText, { color: colors.text }]}>
                {user.rating.toFixed(1)} • {user.completedTasks || 0} trabajos completados
              </Text>
            </View>
          )}
        </View>

        {/* Información Personal - Colapsible */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => toggleSection("personal")}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              👤 Información Personal
            </Text>
            <Text style={[styles.expandIcon, { color: colors.textSecondary }]}>
              {expandedSection === "personal" ? "▼" : "▶"}
            </Text>
          </TouchableOpacity>

          {expandedSection === "personal" && (
            <View style={styles.sectionContent}>
              <TouchableOpacity
                style={[styles.editButton, { backgroundColor: colors.primary }]}
                onPress={() => navigation.navigate("EditProfile")}
              >
                <Text style={styles.editButtonText}>✏️ Editar Perfil</Text>
              </TouchableOpacity>
              
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Email:</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{user?.email}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Teléfono:</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{user?.phone || "No especificado"}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Ubicación:</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{user?.address || "No especificada"}</Text>
              </View>
            </View>
          )}
        </View>

        {/* Mis Habilidades - Siempre visible y destacado */}
        <View style={[styles.skillsSection, { backgroundColor: colors.primary }]}>
          <View style={styles.skillsHeader}>
            <Text style={styles.skillsTitle}>⚡ Mis Habilidades</Text>
            <Text style={styles.skillsSubtitle}>
              {skills.length} habilidades • Mejora tu perfil para más trabajos
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.manageSkillsButton, { backgroundColor: colors.background }]}
            onPress={() => navigation.navigate("Skills")}
          >
            <Text style={[styles.manageSkillsText, { color: colors.primary }]}>
              🎯 Gestionar Habilidades
            </Text>
          </TouchableOpacity>

          <View style={styles.skillsPreview}>
            {skills.slice(0, 3).map((skill) => (
              <View key={skill.id} style={[styles.skillChip, { backgroundColor: colors.background }]}>
                <Text style={[styles.skillChipText, { color: colors.primary }]}>{skill.name}</Text>
              </View>
            ))}
            {skills.length > 3 && (
              <View style={[styles.skillChip, { backgroundColor: colors.background }]}>
                <Text style={[styles.skillChipText, { color: colors.primary }]}>
                  +{skills.length - 3} más
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Portafolio de Trabajos */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => toggleSection("portfolio")}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              📸 Portafolio de Trabajos
            </Text>
            <Text style={[styles.expandIcon, { color: colors.textSecondary }]}>
              {expandedSection === "portfolio" ? "▼" : "▶"}
            </Text>
          </TouchableOpacity>

          {expandedSection === "portfolio" && (
            <View style={styles.sectionContent}>
              <TouchableOpacity
                style={[styles.addButton, { borderColor: colors.border }]}
                onPress={() => setShowWorkSamples(true)}
              >
                <Text style={[styles.addButtonText, { color: colors.primary }]}>
                  📷 Agregar Fotos de Trabajos
                </Text>
              </TouchableOpacity>

              <View style={styles.portfolioGrid}>
                {workSamples.map((sample) => (
                  <View key={sample.id} style={[styles.portfolioItem, { backgroundColor: colors.surfaceGray }]}>
                    <Image source={{ uri: sample.image }} style={styles.portfolioImage} />
                    <Text style={[styles.portfolioTitle, { color: colors.text }]}>{sample.title}</Text>
                    <View style={styles.portfolioRating}>
                      {renderStars(Math.floor(sample.rating))}
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>

        {/* Reseñas y Comentarios */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => toggleSection("reviews")}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              ⭐ Reseñas y Comentarios
            </Text>
            <Text style={[styles.expandIcon, { color: colors.textSecondary }]}>
              {expandedSection === "reviews" ? "▼" : "▶"}
            </Text>
          </TouchableOpacity>

          {expandedSection === "reviews" && (
            <View style={styles.sectionContent}>
              {reviews.map((review) => (
                <View key={review.id} style={[styles.reviewItem, { borderColor: colors.borderLight }]}>
                  <View style={styles.reviewHeader}>
                    <Text style={[styles.reviewClient, { color: colors.text }]}>{review.clientName}</Text>
                    <View style={styles.reviewStars}>
                      {renderStars(Math.floor(review.rating))}
                    </View>
                  </View>
                  <Text style={[styles.reviewTask, { color: colors.textSecondary }]}>{review.taskTitle}</Text>
                  <Text style={[styles.reviewComment, { color: colors.text }]}>{review.comment}</Text>
                  <Text style={[styles.reviewDate, { color: colors.textLight }]}>{review.date}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Botón de Cerrar Sesión */}
        <TouchableOpacity
          style={[styles.logoutButton, { backgroundColor: colors.error }]}
          onPress={logout}
        >
          <Text style={styles.logoutButtonText}>🚪 Cerrar Sesión</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Modal de Notificaciones */}
      <Modal
        visible={showNotifications}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>🔔 Notificaciones</Text>
            <TouchableOpacity onPress={() => setShowNotifications(false)}>
              <Text style={[styles.closeButton, { color: colors.primary }]}>✕</Text>
            </TouchableOpacity>
          </View>

          {unreadCount > 0 && (
            <TouchableOpacity
              style={[styles.markAllButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                markAllAsRead()
                setShowNotifications(false)
              }}
            >
              <Text style={styles.markAllButtonText}>✓ Marcar todas como leídas</Text>
            </TouchableOpacity>
          )}

          <FlatList
            data={notifications}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={[
                styles.notificationItem,
                { 
                  backgroundColor: item.read ? colors.surface : colors.primaryLight,
                  borderColor: colors.borderLight 
                }
              ]}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>{item.title}</Text>
                <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>
                  {item.message}
                </Text>
                <Text style={[styles.notificationDate, { color: colors.textLight }]}>
                  {item.createdAt.toLocaleDateString()}
                </Text>
              </View>
            )}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 32,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  headerTop: {
    width: "100%",
    alignItems: "flex-end",
    marginBottom: 20,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  notificationIcon: {
    fontSize: 20,
  },
  notificationBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 16,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    fontSize: 48,
    fontWeight: "bold",
  },
  editBadge: {
    position: "absolute",
    bottom: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  editBadgeText: {
    fontSize: 16,
  },
  headerName: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 4,
    textAlign: "center",
  },
  headerRole: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  ratingContainer: {
    alignItems: "center",
  },
  starsContainer: {
    flexDirection: "row",
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 14,
    textAlign: "center",
  },
  section: {
    marginHorizontal: 24,
    marginVertical: 8,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  skillsSection: {
    marginHorizontal: 24,
    marginVertical: 8,
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  skillsHeader: {
    marginBottom: 16,
  },
  skillsTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  skillsSubtitle: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.9,
  },
  manageSkillsButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 16,
  },
  manageSkillsText: {
    fontSize: 16,
    fontWeight: "600",
  },
  skillsPreview: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  skillChip: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  skillChipText: {
    fontSize: 12,
    fontWeight: "500",
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  expandIcon: {
    fontSize: 16,
    fontWeight: "bold",
  },
  sectionContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  editButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 16,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  infoValue: {
    fontSize: 14,
    flex: 1,
    textAlign: "right",
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: "dashed",
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  portfolioGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  portfolioItem: {
    width: (width - 80) / 2,
    borderRadius: 12,
    padding: 12,
  },
  portfolioImage: {
    width: "100%",
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  portfolioTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 4,
  },
  portfolioRating: {
    flexDirection: "row",
  },
  reviewItem: {
    borderBottomWidth: 1,
    paddingVertical: 16,
  },
  reviewHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  reviewClient: {
    fontSize: 16,
    fontWeight: "600",
  },
  reviewStars: {
    flexDirection: "row",
  },
  reviewTask: {
    fontSize: 14,
    marginBottom: 8,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  reviewDate: {
    fontSize: 12,
  },
  logoutButton: {
    marginHorizontal: 24,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: "center",
  },
  logoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  modalContainer: {
    flex: 1,
    paddingTop: 60,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "700",
  },
  closeButton: {
    fontSize: 24,
    fontWeight: "bold",
  },
  markAllButton: {
    marginHorizontal: 24,
    marginBottom: 16,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: "center",
  },
  markAllButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  notificationItem: {
    marginHorizontal: 24,
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationDate: {
    fontSize: 12,
  },
})
