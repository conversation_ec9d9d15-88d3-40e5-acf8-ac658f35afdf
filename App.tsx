import { useEffect, useState } from "react"
import { NavigationContainer } from "@react-navigation/native"
import { createStackNavigator, CardStyleInterpolators, TransitionSpecs } from "@react-navigation/stack"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { StatusBar } from "expo-status-bar"
import { Text, View } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { ThemeProvider } from "./contexts/ThemeContext"
import { UserProvider } from "./contexts/UserContext"
import { TaskProvider } from "./contexts/TaskContext"
import { GoalsProvider } from "./contexts/GoalsContext"
import { SkillsProvider } from "./contexts/SkillsContext"
import { NotificationProvider } from "./contexts/NotificationContext"
import { ServicesProvider } from "./contexts/ServicesContext"
import SplashScreen from "./screens/SplashScreen"
import LoginScreen from "./screens/LoginScreen"
import RegisterScreen from "./screens/RegisterScreen"
import HomeScreen from "./screens/HomeScreen"
import TasksScreen from "./screens/TasksScreen"
import ProfileScreen from "./screens/ProfileScreen"
import ChatScreen from "./screens/ChatScreen"
import CalendarScreen from "./screens/CalendarScreen"
import SkillsScreen from "./screens/SkillsScreen"
import JobRecommendationsScreen from "./screens/JobRecommendationsScreen"
import GoalsScreen from "./screens/GoalsScreen"
import TaskDetailScreen from "./screens/TaskDetailScreen"
import CreateTaskScreen from "./screens/CreateTaskScreen"
import MapScreen from "./screens/MapScreen"
import EditProfileScreen from "./screens/EditProfileScreen"
import PaymentsScreen from "./screens/PaymentsScreen"
import CategoryServicesScreen from "./screens/CategoryServicesScreen"
import AllCategoriesScreen from "./screens/AllCategoriesScreen"
import HelpScreen from "./screens/HelpScreen"
import TrustSafetyScreen from "./screens/TrustSafetyScreen"
import PopularProjectsScreen from "./screens/PopularProjectsScreen"
import TaskFlowScreen from "./screens/TaskFlowScreen"
import TaskResultsScreen from "./screens/TaskResultsScreen"
import BookingConfirmationScreen from "./screens/BookingConfirmationScreen"
import ServiceDetailScreen from "./screens/ServiceDetailScreen"
import { useUser } from "./contexts/UserContext"
import { useTheme } from "./contexts/ThemeContext"
import { useNotifications } from "./contexts/NotificationContext"

const Stack = createStackNavigator()
const Tab = createBottomTabNavigator()

function TabNavigator() {
  const { user } = useUser()
  const { colors } = useTheme()
  const { unreadCount } = useNotifications()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarActiveTintColor: colors.primary, // Azul cuando está activo
        tabBarInactiveTintColor: '#9CA3AF', // Gris cuando no está activo
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: "Inicio",
          tabBarIcon: ({ color }) => <Ionicons name="home-outline" size={24} color={color} />,
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Tasks"
        component={TasksScreen}
        options={{
          title: user?.role === "client" ? "Mis Tareas" : "Trabajos",
          tabBarIcon: ({ color }) => <Ionicons name="clipboard-outline" size={24} color={color} />,
        }}
      />
      <Tab.Screen
        name="Calendar"
        component={CalendarScreen}
        options={{
          title: "Calendario",
          tabBarIcon: ({ color }) => <Ionicons name="calendar-outline" size={24} color={color} />,
        }}
      />
      <Tab.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          title: "Metas",
          tabBarIcon: ({ color }) => <Ionicons name="flag-outline" size={24} color={color} />,
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: "Perfil",
          tabBarIcon: ({ color }) => (
            <View style={{ position: "relative" }}>
              <Ionicons name="person-outline" size={24} color={color} />
              {unreadCount > 0 && (
                <View
                  style={{
                    position: "absolute",
                    top: -5,
                    right: -8,
                    backgroundColor: colors.notification,
                    borderRadius: 10,
                    minWidth: 20,
                    height: 20,
                    justifyContent: "center",
                    alignItems: "center",
                    paddingHorizontal: 4,
                  }}
                >
                  <Text
                    style={{
                      color: "#FFFFFF",
                      fontSize: 12,
                      fontWeight: "bold",
                    }}
                  >
                    {unreadCount > 99 ? "99+" : unreadCount}
                  </Text>
                </View>
              )}
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  )
}

function AppNavigator() {
  const { user, isLoading } = useUser()
  const { theme } = useTheme()
  const [showSplash, setShowSplash] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSplash(false)
    }, 2000)
    return () => clearTimeout(timer)
  }, [])

  if (showSplash || isLoading) {
    return <SplashScreen />
  }

  return (
    <NavigationContainer
      theme={{
        dark: theme === "dark",
        colors: {
          primary: "#00D4AA",
          background: theme === "dark" ? "#000000" : "#FFFFFF",
          card: theme === "dark" ? "#1C1C1E" : "#FFFFFF",
          text: theme === "dark" ? "#FFFFFF" : "#000000",
          border: theme === "dark" ? "#38383A" : "#E5E5E7",
          notification: "#FF3B30",
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: theme === "dark" ? "#1C1C1E" : "#FFFFFF",
          },
          headerTintColor: theme === "dark" ? "#FFFFFF" : "#000000",
          cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
          transitionSpec: {
            open: TransitionSpecs.TransitionIOSSpec,
            close: TransitionSpecs.TransitionIOSSpec,
          },
        }}
      >
        {!user ? (
          <>
            <Stack.Screen name="Login" component={LoginScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Register" component={RegisterScreen} options={{ title: "Crear Cuenta" }} />
          </>
        ) : (
          <>
            <Stack.Screen name="Main" component={TabNavigator} options={{ headerShown: false }} />
            <Stack.Screen name="CreateTask" component={CreateTaskScreen} options={{ title: "Solicitar Servicio" }} />
            <Stack.Screen name="TaskDetail" component={TaskDetailScreen} options={{ title: "Detalles de Tarea" }} />
            <Stack.Screen name="Chat" component={ChatScreen} options={{ title: "Chat" }} />
            <Stack.Screen name="Map" component={MapScreen} options={{ title: "Ubicación" }} />
            <Stack.Screen name="Skills" component={SkillsScreen} options={{ title: "Mis Habilidades" }} />
            <Stack.Screen name="JobRecommendations" component={JobRecommendationsScreen} options={{ title: "Trabajos Recomendados" }} />
            <Stack.Screen name="EditProfile" component={EditProfileScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Payments" component={PaymentsScreen} options={{ headerShown: false }} />
            <Stack.Screen name="CategoryServices" component={CategoryServicesScreen} options={{ headerShown: false }} />
            <Stack.Screen name="AllCategories" component={AllCategoriesScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Help" component={HelpScreen} options={{ headerShown: false }} />
            <Stack.Screen name="TrustSafety" component={TrustSafetyScreen} options={{ headerShown: false }} />
            <Stack.Screen name="PopularProjects" component={PopularProjectsScreen} options={{ headerShown: false }} />
            <Stack.Screen name="TaskFlow" component={TaskFlowScreen} options={{ headerShown: false }} />
            <Stack.Screen name="TaskResults" component={TaskResultsScreen} options={{ headerShown: false }} />
            <Stack.Screen name="BookingConfirmation" component={BookingConfirmationScreen} options={{ headerShown: false }} />
            <Stack.Screen name="ServiceDetail" component={ServiceDetailScreen} options={{ headerShown: false }} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  )
}

export default function App() {
  return (
    <ThemeProvider>
      <UserProvider>
        <TaskProvider>
          <GoalsProvider>
            <SkillsProvider>
              <NotificationProvider>
                <ServicesProvider>
                  <AppNavigator />
                  <StatusBar style="auto" />
                </ServicesProvider>
              </NotificationProvider>
            </SkillsProvider>
          </GoalsProvider>
        </TaskProvider>
      </UserProvider>
    </ThemeProvider>
  )
}
