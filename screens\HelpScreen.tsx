import React, { useState } from 'react'
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '../contexts/ThemeContext'

interface ChatMessage {
  id: string
  text: string
  isBot: boolean
  timestamp: Date
}

interface HelpOption {
  id: string
  title: string
  icon: string
  description: string
  action: () => void
}

export default function HelpScreen({ navigation }: any) {
  const { colors } = useTheme()
  const [showChat, setShowChat] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: '¡Hola! Soy tu asistente virtual. ¿En qué puedo ayudarte hoy?',
      isBot: true,
      timestamp: new Date()
    }
  ])
  const [inputText, setInputText] = useState('')

  const helpOptions: HelpOption[] = [
    {
      id: 'complaint',
      title: 'Presentar una queja',
      icon: 'warning-outline',
      description: 'Reportar un problema con un servicio o trabajador',
      action: () => handleQuickResponse('Quiero presentar una queja')
    },
    {
      id: 'payment',
      title: 'Problemas de pago',
      icon: 'card-outline',
      description: 'Ayuda con pagos, reembolsos o facturación',
      action: () => handleQuickResponse('Tengo un problema con un pago')
    },
    {
      id: 'account',
      title: 'Problemas de cuenta',
      icon: 'person-outline',
      description: 'Ayuda con tu perfil, verificación o acceso',
      action: () => handleQuickResponse('Tengo problemas con mi cuenta')
    },
    {
      id: 'service',
      title: 'Calidad del servicio',
      icon: 'star-outline',
      description: 'Reportar problemas con la calidad de un trabajo',
      action: () => handleQuickResponse('Quiero reportar la calidad de un servicio')
    },
    {
      id: 'safety',
      title: 'Seguridad',
      icon: 'shield-outline',
      description: 'Reportar problemas de seguridad o comportamiento',
      action: () => handleQuickResponse('Tengo una preocupación de seguridad')
    },
    {
      id: 'other',
      title: 'Otro tema',
      icon: 'help-circle-outline',
      description: 'Cualquier otra consulta o problema',
      action: () => setShowChat(true)
    }
  ]

  const handleQuickResponse = (message: string) => {
    setShowChat(true)
    addMessage(message, false)
    
    // Simular respuesta del bot
    setTimeout(() => {
      let botResponse = ''
      if (message.includes('queja')) {
        botResponse = 'Entiendo que quieres presentar una queja. Por favor, describe detalladamente el problema que experimentaste. Incluye fecha, nombre del trabajador y qué sucedió exactamente.'
      } else if (message.includes('pago')) {
        botResponse = 'Te ayudo con tu problema de pago. ¿Podrías decirme si es sobre: 1) Un pago que no se procesó, 2) Un reembolso, 3) Una tarjeta rechazada, o 4) Otro problema de facturación?'
      } else if (message.includes('cuenta')) {
        botResponse = 'Puedo ayudarte con problemas de cuenta. ¿Es sobre: 1) No puedes iniciar sesión, 2) Verificación de identidad, 3) Cambiar información personal, o 4) Eliminar tu cuenta?'
      } else if (message.includes('servicio')) {
        botResponse = 'Lamento que hayas tenido problemas con la calidad del servicio. Para ayudarte mejor, necesito que me proporciones: 1) ID de la tarea, 2) Nombre del trabajador, 3) Qué específicamente no cumplió tus expectativas.'
      } else if (message.includes('seguridad')) {
        botResponse = 'La seguridad es nuestra prioridad. Si es una emergencia, contacta inmediatamente a las autoridades. Para otros temas de seguridad, describe la situación y tomaremos acción inmediata.'
      } else {
        botResponse = 'Gracias por contactarnos. Un agente humano revisará tu consulta y te responderá en las próximas 24 horas. ¿Hay algo más en lo que pueda ayudarte mientras tanto?'
      }
      addMessage(botResponse, true)
    }, 1000)
  }

  const addMessage = (text: string, isBot: boolean) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      text,
      isBot,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newMessage])
  }

  const sendMessage = () => {
    if (inputText.trim()) {
      addMessage(inputText, false)
      setInputText('')
      
      // Respuesta automática del bot
      setTimeout(() => {
        addMessage('Gracias por tu mensaje. Un agente revisará tu consulta y te responderá pronto. ¿Hay algo más en lo que pueda ayudarte?', true)
      }, 1000)
    }
  }

  if (showChat) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <TouchableOpacity onPress={() => setShowChat(false)}>
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Chat de Ayuda</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.chatContainer}>
          {messages.map((message) => (
            <View
              key={message.id}
              style={[
                styles.messageContainer,
                message.isBot ? styles.botMessage : styles.userMessage
              ]}
            >
              <Text style={[
                styles.messageText,
                { color: message.isBot ? colors.text : '#FFFFFF' }
              ]}>
                {message.text}
              </Text>
            </View>
          ))}
        </ScrollView>

        <View style={[styles.inputContainer, { backgroundColor: colors.surface }]}>
          <TextInput
            style={[styles.textInput, { color: colors.text, borderColor: colors.border }]}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Escribe tu mensaje..."
            placeholderTextColor={colors.textSecondary}
            multiline
          />
          <TouchableOpacity
            style={[styles.sendButton, { backgroundColor: colors.primary }]}
            onPress={sendMessage}
          >
            <Ionicons name="send" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Ayuda</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.welcomeSection}>
          <Text style={[styles.welcomeTitle, { color: colors.text }]}>
            ¿En qué podemos ayudarte?
          </Text>
          <Text style={[styles.welcomeSubtitle, { color: colors.textSecondary }]}>
            Selecciona una opción o inicia un chat con nuestro asistente
          </Text>
        </View>

        <View style={styles.optionsContainer}>
          {helpOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[styles.optionCard, { backgroundColor: colors.surface }]}
              onPress={option.action}
            >
              <View style={styles.optionLeft}>
                <View style={[styles.optionIcon, { backgroundColor: colors.primaryLight }]}>
                  <Ionicons name={option.icon as any} size={24} color={colors.primary} />
                </View>
                <View style={styles.optionText}>
                  <Text style={[styles.optionTitle, { color: colors.text }]}>
                    {option.title}
                  </Text>
                  <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
                    {option.description}
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={[styles.chatButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowChat(true)}
        >
          <Ionicons name="chatbubble-outline" size={20} color="#FFFFFF" />
          <Text style={styles.chatButtonText}>Iniciar Chat</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  welcomeSection: {
    padding: 20,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    paddingHorizontal: 20,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  chatContainer: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  botMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#6B9BD2',
    padding: 12,
    borderRadius: 16,
    borderBottomRightRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
})
