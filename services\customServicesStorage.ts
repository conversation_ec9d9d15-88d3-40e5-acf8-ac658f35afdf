import AsyncStorage from '@react-native-async-storage/async-storage'

export interface CustomService {
  id: number
  name: string
  category: string
  icon: string
  isCustom: boolean
  createdAt: string
  userId: string
  usageCount?: number // Para trackear qué tan popular es el servicio
}

const CUSTOM_SERVICES_KEY = 'taskapp_custom_services'

export class CustomServicesStorage {
  
  // Obtener todos los servicios personalizados
  static async getCustomServices(): Promise<CustomService[]> {
    try {
      const stored = await AsyncStorage.getItem(CUSTOM_SERVICES_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error al obtener servicios personalizados:', error)
      return []
    }
  }

  // Guardar un nuevo servicio personalizado
  static async saveCustomService(serviceName: string, userId: string = 'current_user'): Promise<CustomService> {
    try {
      const existingServices = await this.getCustomServices()
      
      // Verificar si ya existe un servicio similar
      const existingService = existingServices.find(
        service => service.name.toLowerCase() === serviceName.toLowerCase()
      )
      
      if (existingService) {
        // Si ya existe, incrementar el contador de uso
        existingService.usageCount = (existingService.usageCount || 0) + 1
        await this.updateCustomServices(existingServices)
        return existingService
      }

      // Crear nuevo servicio personalizado
      const newService: CustomService = {
        id: Date.now(),
        name: serviceName.trim(),
        category: 'personalizado',
        icon: '🔧', // Icono por defecto
        isCustom: true,
        createdAt: new Date().toISOString(),
        userId,
        usageCount: 1
      }

      const updatedServices = [...existingServices, newService]
      await this.updateCustomServices(updatedServices)
      
      return newService
    } catch (error) {
      console.error('Error al guardar servicio personalizado:', error)
      throw error
    }
  }

  // Actualizar la lista de servicios personalizados
  static async updateCustomServices(services: CustomService[]): Promise<void> {
    try {
      await AsyncStorage.setItem(CUSTOM_SERVICES_KEY, JSON.stringify(services))
    } catch (error) {
      console.error('Error al actualizar servicios personalizados:', error)
      throw error
    }
  }

  // Obtener servicios más populares (para mostrar primero)
  static async getPopularCustomServices(limit: number = 5): Promise<CustomService[]> {
    try {
      const services = await this.getCustomServices()
      return services
        .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
        .slice(0, limit)
    } catch (error) {
      console.error('Error al obtener servicios populares:', error)
      return []
    }
  }

  // Eliminar un servicio personalizado (para administración)
  static async removeCustomService(serviceId: number): Promise<void> {
    try {
      const services = await this.getCustomServices()
      const filteredServices = services.filter(service => service.id !== serviceId)
      await this.updateCustomServices(filteredServices)
    } catch (error) {
      console.error('Error al eliminar servicio personalizado:', error)
      throw error
    }
  }

  // Obtener estadísticas de servicios personalizados
  static async getCustomServicesStats(): Promise<{
    totalServices: number
    totalUsage: number
    mostPopular: CustomService | null
  }> {
    try {
      const services = await this.getCustomServices()
      const totalUsage = services.reduce((sum, service) => sum + (service.usageCount || 0), 0)
      const mostPopular = services.reduce((prev, current) => 
        (current.usageCount || 0) > (prev?.usageCount || 0) ? current : prev, 
        null as CustomService | null
      )

      return {
        totalServices: services.length,
        totalUsage,
        mostPopular
      }
    } catch (error) {
      console.error('Error al obtener estadísticas:', error)
      return { totalServices: 0, totalUsage: 0, mostPopular: null }
    }
  }
}
