import React from 'react'
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native'
import { useTheme } from '../contexts/ThemeContext'

interface LoadingSpinnerProps {
  size?: 'small' | 'large'
  text?: string
  overlay?: boolean
}

export default function LoadingSpinner({ 
  size = 'large', 
  text = 'Cargando...', 
  overlay = false 
}: LoadingSpinnerProps) {
  const { colors } = useTheme()

  const containerStyle = overlay 
    ? [styles.overlayContainer, { backgroundColor: colors.background + '90' }]
    : [styles.container, { backgroundColor: colors.background }]

  return (
    <View style={containerStyle}>
      <View style={[styles.content, { backgroundColor: colors.surface }]}>
        <ActivityIndicator 
          size={size} 
          color={colors.primary} 
          style={styles.spinner}
        />
        {text && (
          <Text style={[styles.text, { color: colors.text }]}>
            {text}
          </Text>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  content: {
    padding: 30,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  spinner: {
    marginBottom: 16,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
})
