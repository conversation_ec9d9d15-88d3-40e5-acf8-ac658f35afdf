import React, { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

interface Notification {
  id: string
  title: string
  message: string
  type: "task" | "message" | "payment" | "general"
  read: boolean
  createdAt: Date
  data?: any
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  addNotification: (notification: Omit<Notification, "id" | "createdAt" | "read">) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearNotifications: () => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])

  // Cargar notificaciones al iniciar
  useEffect(() => {
    loadNotifications()
  }, [])

  // Guardar notificaciones cuando cambien
  useEffect(() => {
    saveNotifications()
  }, [notifications])

  const loadNotifications = async () => {
    try {
      const stored = await AsyncStorage.getItem("notifications")
      if (stored) {
        const parsed = JSON.parse(stored)
        setNotifications(parsed.map((n: any) => ({
          ...n,
          createdAt: new Date(n.createdAt)
        })))
      }
    } catch (error) {
      console.error("Error loading notifications:", error)
    }
  }

  const saveNotifications = async () => {
    try {
      await AsyncStorage.setItem("notifications", JSON.stringify(notifications))
    } catch (error) {
      console.error("Error saving notifications:", error)
    }
  }

  const addNotification = (notification: Omit<Notification, "id" | "createdAt" | "read">) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      createdAt: new Date(),
      read: false,
    }
    
    setNotifications(prev => [newNotification, ...prev].slice(0, 50)) // Mantener solo 50
  }

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  const clearNotifications = () => {
    setNotifications([])
  }

  const unreadCount = notifications.filter(n => !n.read).length

  // Simular notificaciones de ejemplo
  useEffect(() => {
    const timer = setTimeout(() => {
      if (notifications.length === 0) {
        addNotification({
          title: "¡Bienvenido a TaskApp!",
          message: "Completa tu perfil para encontrar mejores trabajos",
          type: "general"
        })
        
        setTimeout(() => {
          addNotification({
            title: "Nueva tarea disponible",
            message: "Limpieza de casa en Chapinero - $50,000",
            type: "task"
          })
        }, 2000)
        
        setTimeout(() => {
          addNotification({
            title: "Mensaje nuevo",
            message: "Carlos te envió un mensaje sobre la tarea",
            type: "message"
          })
        }, 4000)
      }
    }, 3000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider")
  }
  return context
}
